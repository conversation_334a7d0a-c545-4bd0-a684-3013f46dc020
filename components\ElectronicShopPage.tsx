'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import {
  Smartphone,
  Users,
  Package,
  FileText,
  MessageCircle,
  ArrowRight,
  ArrowLeft,
  Shield,
  Zap,
  RefreshCw,
  CheckCircle,
  Star,
  Award
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function ElectronicShopPage() {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const isRTL = locale === 'ar';

  const switchLanguage = (newLocale: string) => {
    const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);
    router.push(newPath);
  };

  const handleBackToHome = () => {
    router.push(`/${locale}`);
  };

  const handleStartNow = () => {
    router.push(`/${locale}/register?businessModel=electronics`);
  };

  const handleViewDemo = () => {
    router.push(`/${locale}/login?demo=electronics`);
  };

  return (
    <div className={`min-h-screen bg-white ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Back Button */}
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={handleBackToHome}
                className="flex items-center gap-2"
              >
                {isRTL ? <ArrowRight className="w-4 h-4" /> : <ArrowLeft className="w-4 h-4" />}
                {t('nav.home')}
              </Button>

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <Smartphone className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-gray-900">{t('electronics.title')}</h1>
                </div>
              </div>
            </div>

            {/* Language Switcher */}
            <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
              <Button
                variant={locale === 'fr' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => switchLanguage('fr')}
                className="text-xs"
              >
                FR
              </Button>
              <Button
                variant={locale === 'ar' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => switchLanguage('ar')}
                className="text-xs"
              >
                AR
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto">
              <Smartphone className="w-12 h-12 text-white" />
            </div>

            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                {t('electronics.title')}
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('electronics.subtitle')}
              </p>
              <p className="text-lg text-gray-600 max-w-4xl mx-auto">
                {t('electronics.description')}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                onClick={handleStartNow}
              >
                {t('electronics.startNow')}
                <ArrowRight className={`${isRTL ? 'mr-2' : 'ml-2'} h-5 w-5`} />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-gray-300 px-8 py-4 text-lg font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200"
                onClick={handleViewDemo}
              >
                {t('electronics.viewDemo')}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Specialized Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('electronics.features.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {locale === 'ar'
                ? 'مميزات متخصصة لمحلات الإلكترونيات والهواتف'
                : 'Fonctionnalités spécialisées pour les magasins d\'électronique et téléphones'
              }
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Advanced Inventory */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto">
                  <Package className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{t('electronics.features.inventory')}</h3>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {t('electronics.features.inventoryDesc')}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Warranty Tracking */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{t('electronics.features.warranties')}</h3>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {t('electronics.features.warrantiesDesc')}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Professional Invoices */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto">
                  <FileText className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{t('electronics.features.invoices')}</h3>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {t('electronics.features.invoicesDesc')}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* WhatsApp Integration */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-2xl flex items-center justify-center mx-auto">
                  <MessageCircle className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{t('electronics.features.whatsapp')}</h3>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {t('electronics.features.whatsappDesc')}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Customer Returns */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto">
                  <RefreshCw className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{t('electronics.features.returns')}</h3>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {t('electronics.features.returnsDesc')}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Customer Management */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {locale === 'ar' ? 'إدارة الزبائن' : 'Gestion Clients'}
                  </h3>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {locale === 'ar'
                      ? 'دبر زبائنك ومشترياتهم وضماناتهم'
                      : 'Gérez vos clients, leurs achats et garanties'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Why Choose Electronics CRM Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {locale === 'ar' ? 'ليش تختار نظام الإلكترونيات ديالنا؟' : 'Pourquoi choisir notre CRM Électronique?'}
            </h2>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Specialized for Electronics */}
            <div className="text-center space-y-4">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto">
                <Zap className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900">
                {locale === 'ar' ? 'متخصص للإلكترونيات' : 'Spécialisé Électronique'}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {locale === 'ar'
                  ? 'مصمم خصيصا لمحلات الهواتف والإلكترونيات مع مميزات متخصصة'
                  : 'Conçu spécifiquement pour les magasins de téléphones et électronique avec des fonctionnalités spécialisées'
                }
              </p>
            </div>

            {/* Warranty Management */}
            <div className="text-center space-y-4">
              <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-500 rounded-3xl flex items-center justify-center mx-auto">
                <Award className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900">
                {locale === 'ar' ? 'إدارة الضمانات' : 'Gestion Garanties'}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {locale === 'ar'
                  ? 'تتبع ضمانات المنتجات ومطالبات الزبائن بسهولة'
                  : 'Suivez les garanties produits et réclamations clients facilement'
                }
              </p>
            </div>

            {/* WhatsApp Integration */}
            <div className="text-center space-y-4">
              <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-3xl flex items-center justify-center mx-auto">
                <MessageCircle className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900">
                {locale === 'ar' ? 'تكامل واتساب' : 'Intégration WhatsApp'}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {locale === 'ar'
                  ? 'شارك الفواتير والمعلومات مع زبائنك عبر واتساب'
                  : 'Partagez factures et informations avec vos clients via WhatsApp'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Screenshots/Demo Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {locale === 'ar' ? 'شوف النظام كيف خدام' : 'Découvrez l\'interface'}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {locale === 'ar'
                ? 'واجهة متقدمة مصممة خصيصا لمحلات الإلكترونيات'
                : 'Interface avancée conçue spécialement pour les magasins d\'électronique'
              }
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Inventory Management Preview */}
            <div className="space-y-4">
              <div className="bg-gradient-to-br from-blue-50 to-purple-100 rounded-2xl p-8 aspect-[4/3] flex items-center justify-center border border-blue-200">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto">
                    <Package className="w-8 h-8 text-white" />
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-blue-300 rounded w-32 mx-auto"></div>
                    <div className="h-2 bg-blue-200 rounded w-24 mx-auto"></div>
                    <div className="h-2 bg-blue-200 rounded w-28 mx-auto"></div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {locale === 'ar' ? 'إدارة المخزون المتقدمة' : 'Gestion Stock Avancée'}
                </h3>
                <p className="text-sm text-gray-500">
                  {locale === 'ar'
                    ? 'تتبع الأرقام التسلسلية والموديلات'
                    : 'Suivi des numéros de série et modèles'
                  }
                </p>
              </div>
            </div>

            {/* Warranty Tracking Preview */}
            <div className="space-y-4">
              <div className="bg-gradient-to-br from-green-50 to-blue-100 rounded-2xl p-8 aspect-[4/3] flex items-center justify-center border border-green-200">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-green-600 rounded-2xl flex items-center justify-center mx-auto">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-green-300 rounded w-28 mx-auto"></div>
                    <div className="h-2 bg-green-200 rounded w-20 mx-auto"></div>
                    <div className="h-2 bg-green-200 rounded w-24 mx-auto"></div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {locale === 'ar' ? 'تتبع الضمانات' : 'Suivi Garanties'}
                </h3>
                <p className="text-sm text-gray-500">
                  {locale === 'ar'
                    ? 'دبر ضمانات المنتجات والمطالبات'
                    : 'Gérez garanties produits et réclamations'
                  }
                </p>
              </div>
            </div>

            {/* Invoice Generation Preview */}
            <div className="space-y-4">
              <div className="bg-gradient-to-br from-purple-50 to-blue-100 rounded-2xl p-8 aspect-[4/3] flex items-center justify-center border border-purple-200">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-purple-600 rounded-2xl flex items-center justify-center mx-auto">
                    <FileText className="w-8 h-8 text-white" />
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-purple-300 rounded w-30 mx-auto"></div>
                    <div className="h-2 bg-purple-200 rounded w-22 mx-auto"></div>
                    <div className="h-2 bg-purple-200 rounded w-26 mx-auto"></div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {locale === 'ar' ? 'فواتير احترافية' : 'Factures Professionnelles'}
                </h3>
                <p className="text-sm text-gray-500">
                  {locale === 'ar'
                    ? 'أنشئ فواتير مفصلة وشاركها'
                    : 'Créez des factures détaillées et partagez-les'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-br from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8 text-white">
            <div className="space-y-4">
              <h2 className="text-3xl md:text-4xl font-bold">
                {locale === 'ar' ? 'ابدا دبا مجانا' : 'Commencez gratuitement maintenant'}
              </h2>
              <p className="text-xl opacity-90 max-w-2xl mx-auto">
                {locale === 'ar'
                  ? 'انضم لمحلات الإلكترونيات اللي كتستعمل النظام ديالنا'
                  : 'Rejoignez les magasins d\'électronique qui utilisent notre système'
                }
              </p>
            </div>

            <div className="flex flex-wrap justify-center items-center gap-6 text-sm opacity-90">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                <span>{locale === 'ar' ? 'مجاني 100%' : '100% gratuit'}</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                <span>{locale === 'ar' ? 'مميزات متخصصة' : 'Fonctionnalités spécialisées'}</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                <span>{locale === 'ar' ? 'تكامل واتساب' : 'Intégration WhatsApp'}</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                onClick={handleStartNow}
              >
                {t('electronics.startNow')}
                <ArrowRight className={`${isRTL ? 'mr-2' : 'ml-2'} h-5 w-5`} />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200"
                onClick={handleViewDemo}
              >
                {t('electronics.viewDemo')}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <div className="flex items-center justify-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Smartphone className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold">{t('homepage.title')}</h3>
                <p className="text-sm text-gray-400">{t('electronics.subtitle')}</p>
              </div>
            </div>

            <div className="border-t border-gray-800 pt-6">
              <p className="text-sm text-gray-500">
                © 2024 {t('homepage.title')}. {locale === 'ar' ? 'جميع الحقوق محفوظة' : 'Tous droits réservés'}
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
