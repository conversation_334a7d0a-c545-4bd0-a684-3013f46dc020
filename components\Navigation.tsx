'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  ShoppingBag,
  Users,
  CreditCard,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  X,
  Store,
  Globe,
  RotateCcw,
  Bell,
  ChevronDown,
  Search,
  Smartphone
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from '@/components/ui/sheet';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getMessages, type Locale } from '@/lib/i18n';

interface NavigationProps {
  locale: Locale;
  onLocaleChange: (locale: Locale) => void;
}

export default function Navigation({ locale, onLocaleChange }: NavigationProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const messages = getMessages(locale);

  console.log('Navigation rendered with locale:', locale);

  useEffect(() => {
    // Get user data from localStorage
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
      console.log('User data loaded in navigation:', JSON.parse(userData));
    }
  }, []);

  const handleLogout = async () => {
    console.log('Logout initiated from navigation');
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      localStorage.removeItem('auth-token');
      localStorage.removeItem('user');
      console.log('Logout completed, redirecting to login');
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const navigationItems = [
    {
      href: '/dashboard',
      icon: LayoutDashboard,
      label: messages.nav.dashboard,
      color: 'text-emerald-600',
    },
    {
      href: '/products',
      icon: Package,
      label: messages.nav.products,
      color: 'text-blue-600',
    },
    {
      href: '/customers',
      icon: Users,
      label: messages.nav.customers,
      color: 'text-purple-600',
    },
    {
      href: '/sales',
      icon: ShoppingCart,
      label: messages.nav.sales,
      color: 'text-amber-600',
    },
    {
      href: '/purchases',
      icon: ShoppingBag,
      label: messages.nav.purchases,
      color: 'text-green-600',
    },
    {
      href: '/returns',
      icon: RotateCcw,
      label: messages.nav.returns,
      color: 'text-red-600',
    },
    {
      href: '/credit',
      icon: CreditCard,
      label: messages.nav.credit,
      color: 'text-orange-600',
    },
    {
      href: '/analytics',
      icon: BarChart3,
      label: messages.nav.analytics,
      color: 'text-indigo-600',
    },
  ];

  const switchItems = [
    {
      href: '/electronics',
      icon: Smartphone,
      label: 'Electronics CRM',
      color: 'text-blue-600',
      description: 'محل الإلكترونيات'
    },
  ];

  const NavContent = ({ isMobile = false }) => (
    <div className={`flex flex-col ${isMobile ? 'h-full' : 'h-screen'} bg-white border-r border-gray-200`}>
      {/* Brand Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
            <Store className="w-7 h-7 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900 font-dm-sans">Moul Hanout</h1>
            <p className="text-sm text-gray-500 font-inter">Gestion commerciale</p>
          </div>
        </div>
      </div>

      {/* Navigation Links */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href;
          const IconComponent = item.icon;

          return (
            <Link
              key={item.href}
              href={item.href}
              onClick={isMobile ? () => setIsOpen(false) : undefined}
              className={`nav-item ${isActive ? 'active' : ''}`}
              data-macaly={`nav-${item.href.replace('/', '')}`}
            >
              <IconComponent className={`w-5 h-5 mr-4 ${isActive ? 'text-emerald-600' : 'text-gray-500'}`} />
              <span className="font-medium">{item.label}</span>
            </Link>
          );
        })}
      </nav>

      {/* Switch CRM Section */}
      <div className="p-4 border-t border-gray-200">
        <p className="text-xs font-medium text-gray-500 mb-3 uppercase tracking-wide">
          Switch CRM
        </p>
        {switchItems.map((item) => {
          const Icon = item.icon;

          return (
            <Link
              key={item.href}
              href={item.href}
              onClick={() => isMobile && setIsOpen(false)}
              className="flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 group text-gray-600 hover:bg-blue-50 hover:text-blue-700 border border-transparent hover:border-blue-200"
            >
              <Icon className={`w-5 h-5 ${item.color} group-hover:scale-110 transition-transform`} />
              <div className="flex-1">
                <span className="font-medium text-sm">{item.label}</span>
                <p className="text-xs text-gray-500">{item.description}</p>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 space-y-4">
        {/* Language Selector */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-500 flex items-center gap-1">
            <Globe className="w-3 h-3" />
            {messages.nav.language}
          </label>
          <Select value={locale} onValueChange={onLocaleChange}>
            <SelectTrigger className="h-9 text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="fr">🇫🇷 Français</SelectItem>
              <SelectItem value="ar">🇲🇦 العربية</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* User Info & Logout */}
        {user && (
          <div className="space-y-3">
            <div className="text-xs text-gray-500">
              <p className="font-medium text-gray-700">{user.email || user.phone}</p>
            </div>
            <Button
              onClick={handleLogout}
              variant="ghost"
              size="sm"
              className="w-full justify-start gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
              data-macaly="logout-button"
            >
              <LogOut className="w-4 h-4" />
              {messages.nav.logout}
            </Button>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <>
      {/* Desktop Navigation */}
      <div className="hidden lg:block w-64 fixed left-0 top-0 z-30">
        <NavContent />
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="fixed top-4 left-4 z-50 bg-white shadow-md border"
              data-macaly="mobile-menu-trigger"
            >
              <Menu className="w-5 h-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-64">
            <NavContent isMobile />
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
}