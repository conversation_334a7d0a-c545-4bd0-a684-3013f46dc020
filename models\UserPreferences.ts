import mongoose, { Document, Schema } from 'mongoose';

export interface IUserPreferences extends Document {
  userId: mongoose.Types.ObjectId;
  businessModel: 'general' | 'electronics' | null;
  preferredLanguage: 'fr' | 'ar';
  dashboardSettings: {
    theme: 'light' | 'dark';
    defaultView: string;
  };
  onboardingCompleted: boolean;
  lastLogin: Date;
  createdAt: Date;
  updatedAt: Date;
}

const UserPreferencesSchema = new Schema<IUserPreferences>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
    index: true
  },
  businessModel: {
    type: String,
    enum: ['general', 'electronics', null],
    default: null
  },
  preferredLanguage: {
    type: String,
    enum: ['fr', 'ar'],
    default: 'fr'
  },
  dashboardSettings: {
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'light'
    },
    defaultView: {
      type: String,
      default: 'dashboard'
    }
  },
  onboardingCompleted: {
    type: Boolean,
    default: false
  },
  lastLogin: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for efficient lookups
UserPreferencesSchema.index({ userId: 1 });

// Method to update business model choice
UserPreferencesSchema.methods.setBusinessModel = function(model: 'general' | 'electronics') {
  this.businessModel = model;
  this.onboardingCompleted = true;
  this.lastLogin = new Date();
  return this.save();
};

// Method to get redirect URL based on business model
UserPreferencesSchema.methods.getRedirectUrl = function() {
  switch (this.businessModel) {
    case 'electronics':
      return '/electronics';
    case 'general':
      return '/dashboard';
    default:
      return '/'; // Back to landing page to choose
  }
};

export default mongoose.models.UserPreferences || mongoose.model<IUserPreferences>('UserPreferences', UserPreferencesSchema);
