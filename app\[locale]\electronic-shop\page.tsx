import { useTranslations } from 'next-intl';
import { setRequestLocale } from 'next-intl/server';
import { Metadata } from 'next';
import ElectronicShopPage from '@/components/ElectronicShopPage';

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const title = locale === 'ar'
    ? 'محل الإلكترونيات - للهواتف والإكسسوارات'
    : 'Magasin d\'Électronique – Pour téléphones et accessoires';

  const description = locale === 'ar'
    ? 'نظام إدارة متقدم لمحلات الهواتف والأجهزة الإلكترونية والإكسسوارات.'
    : 'Système de gestion avancé pour les magasins de téléphones, appareils électroniques et accessoires.';

  return {
    title,
    description,
    keywords: locale === 'ar'
      ? 'إلكترونيات, هواتف, إكسسوارات, ضمانات, فواتير, واتساب, المغرب'
      : 'électronique, téléphones, accessoires, garanties, factures, WhatsApp, Maroc',
    openGraph: {
      title,
      description,
      type: 'website',
      locale: locale === 'ar' ? 'ar_MA' : 'fr_MA',
    },
  };
}

export default async function Page({ params }: Props) {
  const { locale } = await params;

  setRequestLocale(locale);
  return <ElectronicShopPage />;
}

export function generateStaticParams() {
  return [{ locale: 'fr' }, { locale: 'ar' }];
}
