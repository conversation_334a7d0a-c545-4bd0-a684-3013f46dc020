import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import ElectronicsProduct from '@/models/ElectronicsProduct';
import ElectronicsInvoice from '@/models/ElectronicsInvoice';

export async function GET(request: NextRequest) {
  try {
    console.log('Electronics notifications request received');
    
    // Check authentication
    const user = getUserFromRequest(request);
    if (!user) {
      console.log('Unauthorized electronics notifications access attempt');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const userId = user.id;
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());

    // Count low stock items
    const lowStockCount = await ElectronicsProduct.countDocuments({
      userId,
      isActive: true,
      $expr: { $lte: ['$quantity', '$minStockLevel'] }
    });

    // Count warranties expiring in the next month
    const expiringWarrantiesCount = await ElectronicsInvoice.aggregate([
      { $match: { userId, isActive: true } },
      { $unwind: '$items' },
      { 
        $match: { 
          'items.warrantyExpiry': { 
            $gt: now,
            $lte: nextMonth
          }
        } 
      },
      { $count: 'total' }
    ]);

    // Count unpaid invoices
    const unpaidInvoicesCount = await ElectronicsInvoice.countDocuments({
      userId,
      isActive: true,
      paymentStatus: { $in: ['unpaid', 'partial'] }
    });

    const totalNotifications = lowStockCount + 
                              (expiringWarrantiesCount[0]?.total || 0) + 
                              unpaidInvoicesCount;

    console.log(`Electronics notifications count: ${totalNotifications}`);

    return NextResponse.json({
      count: totalNotifications,
      breakdown: {
        lowStock: lowStockCount,
        expiringWarranties: expiringWarrantiesCount[0]?.total || 0,
        unpaidInvoices: unpaidInvoicesCount
      }
    });

  } catch (error) {
    console.error('Electronics notifications error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
}
