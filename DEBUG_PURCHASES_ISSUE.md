# Debug: Purchases Page Client-Side Error

## Issue
When visiting `http://localhost:3000/purchases`, you get:
```
Application error: a client-side exception has occurred while loading localhost
```

## Potential Causes & Fixes Applied

### 1. Missing i18n Messages ✅ FIXED
**Problem**: The purchases page was trying to access `messages.purchases.title` but the purchases section was missing from i18n.
**Fix**: Added complete purchases translations to `lib/i18n.ts`:

```typescript
// French
purchases: {
  title: 'Gestion des achats',
  addPurchase: 'Nouvel achat',
  product: 'Produit',
  supplier: 'Fournisseur',
  // ... more translations
},

// Arabic  
purchases: {
  title: 'تدبير المشتريات',
  addPurchase: 'شراء جديد',
  product: 'المنتوج',
  supplier: 'المورد',
  // ... more translations
},
```

### 2. Date-fns Arabic Locale Issue ✅ FIXED
**Problem**: Importing `ar` locale from date-fns which might not exist.
**Fix**: Removed Arabic locale import and used French locale as fallback:

```typescript
// Before
import { fr, ar } from 'date-fns/locale';
const dateLocale = locale === 'ar' ? ar : fr;

// After  
import { fr } from 'date-fns/locale';
// Use French locale for both languages
```

### 3. Added Error Handling ✅ FIXED
**Problem**: Potential crashes from undefined data or date formatting errors.
**Fix**: Added defensive programming:

```typescript
// Safe data access
setPurchases(purchasesData.purchases || []);
setProducts(productsData.products || []);

// Safe message access
{messages.purchases?.title || 'Gestion des achats'}

// Safe date formatting with try-catch
const formatDate = (date: string) => {
  try {
    return format(new Date(date), 'dd MMM yyyy HH:mm', { locale: fr });
  } catch (error) {
    return new Date(date).toLocaleDateString();
  }
};
```

## How to Debug Further

### 1. Check Browser Console
Open browser dev tools (F12) and look for specific error messages:
- JavaScript errors
- Network request failures
- Import/module errors

### 2. Check Network Tab
Verify API calls are working:
- `/api/purchases` should return 200
- `/api/products` should return 200
- Check response data structure

### 3. Test Step by Step
1. **Authentication**: Ensure you're logged in
2. **API Access**: Test API endpoints directly
3. **Component Rendering**: Check if basic page structure loads

### 4. Temporary Debug Version
Create a minimal version of the purchases page to isolate the issue:

```typescript
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function PurchasesDebug() {
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    try {
      console.log('Purchases page loading...');
      
      // Check auth
      const token = localStorage.getItem('auth-token');
      if (!token) {
        router.push('/login');
        return;
      }
      
      console.log('Auth check passed');
      setLoading(false);
    } catch (err) {
      console.error('Error in useEffect:', err);
      setError(err.message);
      setLoading(false);
    }
  }, [router]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h1>Purchases Page Debug</h1>
      <p>If you see this, the basic page structure works!</p>
    </div>
  );
}
```

## Files Modified
- ✅ `lib/i18n.ts` - Added purchases translations
- ✅ `app/purchases/page.tsx` - Fixed imports and added error handling
- ✅ `components/ErrorBoundary.tsx` - Created error boundary component

## Next Steps
1. Clear browser cache and try again
2. Check browser console for specific error messages
3. Test with the debug version if needed
4. Verify all dependencies are installed correctly

The fixes should resolve the most common causes of client-side errors in React/Next.js applications.
