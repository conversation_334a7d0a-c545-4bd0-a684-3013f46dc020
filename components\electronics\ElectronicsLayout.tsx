'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  LayoutDashboard,
  Package,
  Users,
  FileText,
  Shield,
  CreditCard,
  BarChart3,
  Settings,
  Menu,
  X,
  Smartphone,
  Bell,
  Search,
  Store
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

interface ElectronicsLayoutProps {
  children: React.ReactNode;
}

const navigationItems = [
  {
    href: '/electronics',
    icon: LayoutDashboard,
    label: 'Dashboard',
    color: 'text-blue-600',
  },
  {
    href: '/electronics/inventory',
    icon: Package,
    label: 'Inventory',
    color: 'text-green-600',
  },
  {
    href: '/electronics/customers',
    icon: Users,
    label: 'Customers',
    color: 'text-purple-600',
  },
  {
    href: '/electronics/invoices',
    icon: FileText,
    label: 'Invoices',
    color: 'text-orange-600',
  },
  {
    href: '/electronics/warranty',
    icon: Shield,
    label: 'Warranty',
    color: 'text-red-600',
  },
  {
    href: '/electronics/payments',
    icon: CreditCard,
    label: 'Payments',
    color: 'text-indigo-600',
  },
  {
    href: '/electronics/reports',
    icon: BarChart3,
    label: 'Reports',
    color: 'text-teal-600',
  },
  {
    href: '/electronics/settings',
    icon: Settings,
    label: 'Settings',
    color: 'text-gray-600',
  },
];

export default function ElectronicsLayout({ children }: ElectronicsLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [notifications, setNotifications] = useState(0);
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    // Fetch notifications count
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      const response = await fetch('/api/electronics/notifications');
      if (response.ok) {
        const data = await response.json();
        setNotifications(data.count || 0);
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    }
  };

  const NavContent = ({ isMobile = false }: { isMobile?: boolean }) => (
    <div className="flex flex-col h-full bg-white border-r border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Smartphone className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gray-900">Electronics CRM</h2>
            <p className="text-xs text-gray-500">Shop Management</p>
          </div>
        </div>
        {isMobile && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarOpen(false)}
            className="p-2"
          >
            <X className="w-5 h-5" />
          </Button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
          const Icon = item.icon;

          return (
            <Link
              key={item.href}
              href={item.href}
              onClick={() => isMobile && setSidebarOpen(false)}
              className={`flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 group ${isActive
                ? 'bg-blue-50 text-blue-700 border border-blue-200'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
            >
              <Icon className={`w-5 h-5 ${isActive ? 'text-blue-600' : item.color} group-hover:scale-110 transition-transform`} />
              <span className="font-medium">{item.label}</span>
              {item.label === 'Warranty' && notifications > 0 && (
                <Badge variant="destructive" className="ml-auto text-xs">
                  {notifications}
                </Badge>
              )}
            </Link>
          );
        })}
      </nav>



      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          <p>Electronics CRM v1.0</p>
          <p>© 2024 Your Shop</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Desktop Sidebar */}
      <div className="hidden lg:block w-64 fixed left-0 top-0 h-full z-30">
        <NavContent />
      </div>

      {/* Mobile Sidebar */}
      {sidebarOpen && (
        <div className="lg:hidden fixed inset-0 z-50">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setSidebarOpen(false)} />
          <div className="absolute left-0 top-0 w-64 h-full">
            <NavContent isMobile />
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 lg:ml-64">
        {/* Top Bar */}
        <header className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden p-2"
              >
                <Menu className="w-5 h-5" />
              </Button>

              <div className="hidden md:block">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    placeholder="Search products, customers, invoices..."
                    className="pl-10 w-80"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="w-5 h-5" />
                {notifications > 0 && (
                  <Badge variant="destructive" className="absolute -top-1 -right-1 text-xs min-w-[1.25rem] h-5">
                    {notifications}
                  </Badge>
                )}
              </Button>


            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
