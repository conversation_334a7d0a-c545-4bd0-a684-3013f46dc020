import mongoose, { Document, Schema } from 'mongoose';

export interface IInvoiceItem {
  productId: mongoose.Types.ObjectId;
  productName: string;
  sku: string;
  quantity: number;
  unitPrice: number;
  warrantyMonths: number;
  warrantyExpiry: Date;
  serialNumbers?: string[];
  discount?: number;
  subtotal: number;
}

export interface IElectronicsInvoice extends Document {
  userId: mongoose.Types.ObjectId;
  invoiceNumber: string;
  customerId?: mongoose.Types.ObjectId;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  items: IInvoiceItem[];
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  discount: number;
  totalAmount: number;
  paymentStatus: 'unpaid' | 'partial' | 'paid';
  paidAmount: number;
  remainingAmount: number;
  paymentMethod?: 'cash' | 'card' | 'bank_transfer' | 'mobile_payment' | 'credit';
  paymentDate?: Date;
  dueDate?: Date;
  notes?: string;
  shopInfo: {
    name: string;
    address: string;
    phone: string;
    email?: string;
    logo?: string;
    taxId?: string;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const InvoiceItemSchema = new Schema<IInvoiceItem>({
  productId: {
    type: Schema.Types.ObjectId,
    ref: 'ElectronicsProduct',
    required: true
  },
  productName: {
    type: String,
    required: true
  },
  sku: {
    type: String,
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  unitPrice: {
    type: Number,
    required: true,
    min: 0
  },
  warrantyMonths: {
    type: Number,
    required: true,
    min: 0
  },
  warrantyExpiry: {
    type: Date,
    required: true
  },
  serialNumbers: [String],
  discount: {
    type: Number,
    default: 0,
    min: 0
  },
  subtotal: {
    type: Number,
    required: true,
    min: 0
  }
});

const ElectronicsInvoiceSchema = new Schema<IElectronicsInvoice>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  invoiceNumber: {
    type: String,
    required: true,
    unique: true
  },
  customerId: {
    type: Schema.Types.ObjectId,
    ref: 'ElectronicsCustomer'
  },
  customerName: {
    type: String,
    required: true,
    trim: true
  },
  customerPhone: {
    type: String,
    required: true,
    trim: true
  },
  customerEmail: {
    type: String,
    trim: true,
    lowercase: true
  },
  items: [InvoiceItemSchema],
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  taxRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  taxAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  discount: {
    type: Number,
    default: 0,
    min: 0
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  paymentStatus: {
    type: String,
    enum: ['unpaid', 'partial', 'paid'],
    default: 'unpaid'
  },
  paidAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  remainingAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'bank_transfer', 'mobile_payment', 'credit']
  },
  paymentDate: Date,
  dueDate: Date,
  notes: {
    type: String,
    trim: true
  },
  shopInfo: {
    name: {
      type: String,
      required: true
    },
    address: {
      type: String,
      required: true
    },
    phone: {
      type: String,
      required: true
    },
    email: String,
    logo: String,
    taxId: String
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for better performance
ElectronicsInvoiceSchema.index({ userId: 1, invoiceNumber: 1 });
ElectronicsInvoiceSchema.index({ userId: 1, customerId: 1 });
ElectronicsInvoiceSchema.index({ userId: 1, paymentStatus: 1 });
ElectronicsInvoiceSchema.index({ userId: 1, createdAt: -1 });
ElectronicsInvoiceSchema.index({ userId: 1, dueDate: 1 });

// Pre-save middleware to calculate totals and warranty expiry
ElectronicsInvoiceSchema.pre('save', function (next) {
  // Calculate subtotal from items
  this.subtotal = this.items.reduce((sum, item) => sum + item.subtotal, 0);

  // Calculate tax amount
  this.taxAmount = (this.subtotal * this.taxRate) / 100;

  // Calculate total amount
  this.totalAmount = this.subtotal + this.taxAmount - this.discount;

  // Calculate remaining amount
  this.remainingAmount = Math.max(0, this.totalAmount - this.paidAmount);

  // Update payment status
  if (this.paidAmount === 0) {
    this.paymentStatus = 'unpaid';
  } else if (this.paidAmount >= this.totalAmount) {
    this.paymentStatus = 'paid';
    this.remainingAmount = 0;
  } else {
    this.paymentStatus = 'partial';
  }

  // Calculate warranty expiry for each item
  this.items.forEach(item => {
    const saleDate = this.createdAt || new Date();
    item.warrantyExpiry = new Date(saleDate.getTime() + (item.warrantyMonths * 30 * 24 * 60 * 60 * 1000));
  });

  next();
});

// Static method to generate invoice number
ElectronicsInvoiceSchema.statics.generateInvoiceNumber = async function (userId: mongoose.Types.ObjectId) {
  const year = new Date().getFullYear();
  const month = String(new Date().getMonth() + 1).padStart(2, '0');

  const lastInvoice = await this.findOne(
    { userId },
    {},
    { sort: { createdAt: -1 } }
  );

  let sequence = 1;
  if (lastInvoice && lastInvoice.invoiceNumber) {
    const lastSequence = parseInt(lastInvoice.invoiceNumber.split('-').pop() || '0');
    sequence = lastSequence + 1;
  }

  return `INV-${year}${month}-${String(sequence).padStart(4, '0')}`;
};

// Method to add payment
ElectronicsInvoiceSchema.methods.addPayment = function (amount: number, method: string, notes?: string) {
  this.paidAmount += amount;
  this.paymentMethod = method;
  if (this.paidAmount >= this.totalAmount) {
    this.paymentDate = new Date();
  }
  return this.save();
};

export default mongoose.models.ElectronicsInvoice || mongoose.model<IElectronicsInvoice>('ElectronicsInvoice', ElectronicsInvoiceSchema);
