import mongoose, { Document, Schema } from 'mongoose';

export interface IElectronicsPayment extends Document {
  userId: mongoose.Types.ObjectId;
  invoiceId: mongoose.Types.ObjectId;
  customerId?: mongoose.Types.ObjectId;
  amount: number;
  paymentMethod: 'cash' | 'card' | 'bank_transfer' | 'mobile_payment' | 'credit_adjustment';
  paymentDate: Date;
  reference?: string;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ElectronicsPaymentSchema = new Schema<IElectronicsPayment>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  invoiceId: {
    type: Schema.Types.ObjectId,
    ref: 'ElectronicsInvoice',
    required: true
  },
  customerId: {
    type: Schema.Types.ObjectId,
    ref: 'ElectronicsCustomer'
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'bank_transfer', 'mobile_payment', 'credit_adjustment'],
    required: true
  },
  paymentDate: {
    type: Date,
    default: Date.now
  },
  reference: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for better performance
ElectronicsPaymentSchema.index({ userId: 1, invoiceId: 1 });
ElectronicsPaymentSchema.index({ userId: 1, customerId: 1 });
ElectronicsPaymentSchema.index({ userId: 1, paymentDate: -1 });
ElectronicsPaymentSchema.index({ userId: 1, paymentMethod: 1 });

export default mongoose.models.ElectronicsPayment || mongoose.model<IElectronicsPayment>('ElectronicsPayment', ElectronicsPaymentSchema);
