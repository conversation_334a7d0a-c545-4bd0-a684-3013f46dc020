import mongoose, { Document, Schema } from 'mongoose';

export interface IElectronicsProduct extends Document {
  userId: mongoose.Types.ObjectId;
  name: string;
  sku: string;
  brand: string;
  category: 'phone' | 'accessory' | 'tablet' | 'laptop' | 'smartwatch' | 'headphones' | 'charger' | 'case' | 'other';
  quantity: number;
  minStockLevel: number;
  warrantyDuration: number; // in months
  costPrice: number;
  sellPrice: number;
  description?: string;
  specifications?: {
    model?: string;
    color?: string;
    storage?: string;
    ram?: string;
    screenSize?: string;
    batteryCapacity?: string;
    operatingSystem?: string;
    connectivity?: string[];
  };
  supplier?: {
    name: string;
    contact: string;
    email?: string;
  };
  images?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ElectronicsProductSchema = new Schema<IElectronicsProduct>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  sku: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true
  },
  brand: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    enum: ['phone', 'accessory', 'tablet', 'laptop', 'smartwatch', 'headphones', 'charger', 'case', 'other'],
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  minStockLevel: {
    type: Number,
    required: true,
    min: 0,
    default: 5
  },
  warrantyDuration: {
    type: Number,
    required: true,
    min: 0,
    default: 12 // 12 months default
  },
  costPrice: {
    type: Number,
    required: true,
    min: 0
  },
  sellPrice: {
    type: Number,
    required: true,
    min: 0
  },
  description: {
    type: String,
    trim: true
  },
  specifications: {
    model: String,
    color: String,
    storage: String,
    ram: String,
    screenSize: String,
    batteryCapacity: String,
    operatingSystem: String,
    connectivity: [String]
  },
  supplier: {
    name: String,
    contact: String,
    email: String
  },
  images: [String],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for better performance
ElectronicsProductSchema.index({ userId: 1, sku: 1 });
ElectronicsProductSchema.index({ userId: 1, category: 1 });
ElectronicsProductSchema.index({ userId: 1, brand: 1 });
ElectronicsProductSchema.index({ userId: 1, quantity: 1 });
ElectronicsProductSchema.index({ userId: 1, isActive: 1 });

// Virtual for profit margin
ElectronicsProductSchema.virtual('profitMargin').get(function() {
  return this.sellPrice - this.costPrice;
});

// Virtual for profit percentage
ElectronicsProductSchema.virtual('profitPercentage').get(function() {
  return this.costPrice > 0 ? ((this.sellPrice - this.costPrice) / this.costPrice) * 100 : 0;
});

// Virtual for low stock status
ElectronicsProductSchema.virtual('isLowStock').get(function() {
  return this.quantity <= this.minStockLevel;
});

// Pre-save middleware to generate SKU if not provided
ElectronicsProductSchema.pre('save', function(next) {
  if (!this.sku) {
    const brandCode = this.brand.substring(0, 3).toUpperCase();
    const categoryCode = this.category.substring(0, 3).toUpperCase();
    const timestamp = Date.now().toString().slice(-6);
    this.sku = `${brandCode}${categoryCode}${timestamp}`;
  }
  next();
});

export default mongoose.models.ElectronicsProduct || mongoose.model<IElectronicsProduct>('ElectronicsProduct', ElectronicsProductSchema);
