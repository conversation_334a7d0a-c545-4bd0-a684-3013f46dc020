// Database migration script to fix User model indexes
// Run this script to fix the duplicate key error for email/phone registration

const { MongoClient } = require('mongodb');

// MongoDB connection URL - update this to match your database
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crm-hanout';

async function fixUserIndexes() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    console.log('🔗 Connecting to MongoDB...');
    await client.connect();
    
    const db = client.db();
    const usersCollection = db.collection('users');
    
    console.log('📋 Current indexes:');
    const currentIndexes = await usersCollection.indexes();
    currentIndexes.forEach(index => {
      console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
      if (index.sparse) console.log(`    (sparse: ${index.sparse})`);
      if (index.unique) console.log(`    (unique: ${index.unique})`);
    });
    
    console.log('\n🗑️  Dropping old email index...');
    try {
      await usersCollection.dropIndex('email_1');
      console.log('✅ Old email index dropped');
    } catch (error) {
      if (error.code === 27) {
        console.log('ℹ️  Email index already doesn\'t exist');
      } else {
        console.log('⚠️  Error dropping email index:', error.message);
      }
    }
    
    console.log('🗑️  Dropping old phone index...');
    try {
      await usersCollection.dropIndex('phone_1');
      console.log('✅ Old phone index dropped');
    } catch (error) {
      if (error.code === 27) {
        console.log('ℹ️  Phone index already doesn\'t exist');
      } else {
        console.log('⚠️  Error dropping phone index:', error.message);
      }
    }
    
    console.log('\n🔧 Creating new sparse unique indexes...');
    
    // Create sparse unique index for email
    await usersCollection.createIndex(
      { email: 1 }, 
      { 
        unique: true, 
        sparse: true,
        name: 'email_sparse_unique'
      }
    );
    console.log('✅ Created sparse unique email index');
    
    // Create sparse unique index for phone
    await usersCollection.createIndex(
      { phone: 1 }, 
      { 
        unique: true, 
        sparse: true,
        name: 'phone_sparse_unique'
      }
    );
    console.log('✅ Created sparse unique phone index');
    
    console.log('\n📋 New indexes:');
    const newIndexes = await usersCollection.indexes();
    newIndexes.forEach(index => {
      console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
      if (index.sparse) console.log(`    (sparse: ${index.sparse})`);
      if (index.unique) console.log(`    (unique: ${index.unique})`);
    });
    
    console.log('\n🎉 Index migration completed successfully!');
    console.log('You can now register users with email only, phone only, or both.');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await client.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the migration
if (require.main === module) {
  fixUserIndexes().catch(console.error);
}

module.exports = { fixUserIndexes };
