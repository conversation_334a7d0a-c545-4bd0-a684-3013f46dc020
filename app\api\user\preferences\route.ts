import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import UserPreferences from '@/models/UserPreferences';

export async function GET(request: NextRequest) {
  try {
    console.log('User preferences GET request received');

    // Check authentication
    const user = getUserFromRequest(request);
    if (!user) {
      console.log('Unauthorized user preferences access attempt');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Find or create user preferences
    let preferences = await UserPreferences.findOne({ userId: user.userId });

    if (!preferences) {
      preferences = new UserPreferences({
        userId: user.userId,
        businessModel: null,
        preferredLanguage: 'fr',
        onboardingCompleted: false
      });
      await preferences.save();
    }

    console.log(`User preferences retrieved for user: ${user.userId}`);

    return NextResponse.json({
      success: true,
      preferences: {
        businessModel: preferences.businessModel,
        preferredLanguage: preferences.preferredLanguage,
        onboardingCompleted: preferences.onboardingCompleted,
        redirectUrl: preferences.getRedirectUrl()
      }
    });

  } catch (error) {
    console.error('User preferences GET error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user preferences' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('User preferences POST request received');

    // Check authentication
    const user = getUserFromRequest(request);
    if (!user) {
      console.log('Unauthorized user preferences update attempt');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const body = await request.json();
    const { businessModel, preferredLanguage } = body;

    console.log('Updating user preferences:', { businessModel, preferredLanguage });

    // Validate business model
    if (businessModel && !['general', 'electronics'].includes(businessModel)) {
      return NextResponse.json(
        { error: 'Invalid business model' },
        { status: 400 }
      );
    }

    // Find or create user preferences
    let preferences = await UserPreferences.findOne({ userId: user.userId });

    if (!preferences) {
      preferences = new UserPreferences({
        userId: user.userId,
        businessModel: businessModel || null,
        preferredLanguage: preferredLanguage || 'fr',
        onboardingCompleted: !!businessModel
      });
    } else {
      if (businessModel) {
        preferences.businessModel = businessModel;
        preferences.onboardingCompleted = true;
      }
      if (preferredLanguage) {
        preferences.preferredLanguage = preferredLanguage;
      }
      preferences.lastLogin = new Date();
    }

    await preferences.save();

    console.log(`User preferences updated for user: ${user.userId}`);

    return NextResponse.json({
      success: true,
      preferences: {
        businessModel: preferences.businessModel,
        preferredLanguage: preferences.preferredLanguage,
        onboardingCompleted: preferences.onboardingCompleted,
        redirectUrl: preferences.getRedirectUrl()
      }
    });

  } catch (error) {
    console.error('User preferences POST error:', error);
    return NextResponse.json(
      { error: 'Failed to update user preferences' },
      { status: 500 }
    );
  }
}
