import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import ElectronicsInvoice from '@/models/ElectronicsInvoice';
import ElectronicsCustomer from '@/models/ElectronicsCustomer';
import ElectronicsProduct from '@/models/ElectronicsProduct';

export async function GET(request: NextRequest) {
  try {
    console.log('Electronics dashboard activities request received');
    
    // Check authentication
    const user = getUserFromRequest(request);
    if (!user) {
      console.log('Unauthorized electronics dashboard activities access attempt');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const userId = user.id;
    const limit = 10; // Show last 10 activities

    // Get recent invoices
    const recentInvoices = await ElectronicsInvoice.find({
      userId,
      isActive: true
    })
    .sort({ createdAt: -1 })
    .limit(5)
    .select('invoiceNumber customerName totalAmount paymentStatus createdAt')
    .lean();

    // Get recent customers
    const recentCustomers = await ElectronicsCustomer.find({
      userId,
      isActive: true
    })
    .sort({ createdAt: -1 })
    .limit(3)
    .select('name phone customerType createdAt')
    .lean();

    // Get recently added products
    const recentProducts = await ElectronicsProduct.find({
      userId,
      isActive: true
    })
    .sort({ createdAt: -1 })
    .limit(3)
    .select('name brand category quantity createdAt')
    .lean();

    // Combine and format activities
    const activities = [];

    // Add invoice activities
    recentInvoices.forEach(invoice => {
      activities.push({
        id: invoice._id,
        type: 'invoice',
        title: `Invoice ${invoice.invoiceNumber} created`,
        description: `${invoice.customerName} - ${invoice.totalAmount.toLocaleString('fr-FR')} MAD`,
        status: invoice.paymentStatus,
        timestamp: invoice.createdAt,
        icon: 'FileText'
      });
    });

    // Add customer activities
    recentCustomers.forEach(customer => {
      activities.push({
        id: customer._id,
        type: 'customer',
        title: 'New customer added',
        description: `${customer.name} (${customer.phone})`,
        status: customer.customerType,
        timestamp: customer.createdAt,
        icon: 'Users'
      });
    });

    // Add product activities
    recentProducts.forEach(product => {
      activities.push({
        id: product._id,
        type: 'product',
        title: 'Product added to inventory',
        description: `${product.brand} ${product.name} (${product.quantity} units)`,
        status: 'active',
        timestamp: product.createdAt,
        icon: 'Package'
      });
    });

    // Sort all activities by timestamp (most recent first)
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Take only the most recent activities
    const limitedActivities = activities.slice(0, limit);

    console.log(`Electronics dashboard activities compiled: ${limitedActivities.length} activities`);

    return NextResponse.json({
      activities: limitedActivities,
      total: activities.length
    });

  } catch (error) {
    console.error('Electronics dashboard activities error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard activities' },
      { status: 500 }
    );
  }
}
