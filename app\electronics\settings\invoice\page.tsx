'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Save,
  Upload,
  Eye,
  Download,
  QrCode,
  Globe,
  Image,
  Layout,
  FileText,
  Building
} from 'lucide-react';
import InvoicePreview from '@/components/electronics/InvoicePreview';

interface InvoiceSettings {
  // Company Information (Moroccan Standards)
  companyName: string;
  companyAddress: string;
  ice: string; // Identifiant Commun de l'Entreprise
  rc: string; // Registre de Commerce
  cnss: string; // CNSS (if applicable)
  taxId: string; // IF (Identifiant Fiscal)
  phone: string;
  email: string;
  website: string;

  // Logo and Branding
  logo: string | null;
  logoPosition: 'left' | 'center' | 'right';

  // Layout and Design
  template: 'simple' | 'compact' | 'signature';
  primaryColor: string;
  secondaryColor: string;
  fontSize: 'small' | 'medium' | 'large';

  // Formatting
  currency: string;
  locale: 'fr-MA' | 'ar-MA' | 'en-US';
  dateFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';

  // Features
  enableQrCode: boolean;
  qrCodeContent: 'invoice_url' | 'company_info' | 'payment_info';
  autoGenerateNumber: boolean;
  invoicePrefix: string;
  invoiceCounter: number;

  // Multi-language
  defaultLanguage: 'fr' | 'ar' | 'en';
  enableMultiLanguage: boolean;

  // Footer and Terms
  footerText: string;
  termsAndConditions: string;
  bankDetails: string;

  // Signature
  enableSignature: boolean;
  signatureText: string;
}

export default function InvoiceSettingsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [saved, setSaved] = useState(false);
  const [previewInvoice, setPreviewInvoice] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [settings, setSettings] = useState<InvoiceSettings>({
    // Company Information
    companyName: 'Electronics Pro Shop',
    companyAddress: '123 Avenue Mohammed V, Quartier Maarif\n20000 Casablanca, Maroc',
    ice: '0*************3',
    rc: '12345',
    cnss: '1234567',
    taxId: '**********',
    phone: '+*********** 456',
    email: '<EMAIL>',
    website: 'www.electronicspro.ma',

    // Logo and Branding
    logo: null,
    logoPosition: 'left',

    // Layout and Design
    template: 'simple',
    primaryColor: '#2563eb',
    secondaryColor: '#64748b',
    fontSize: 'medium',

    // Formatting
    currency: 'MAD',
    locale: 'fr-MA',
    dateFormat: 'DD/MM/YYYY',

    // Features
    enableQrCode: true,
    qrCodeContent: 'invoice_url',
    autoGenerateNumber: true,
    invoicePrefix: 'FAC',
    invoiceCounter: 1001,

    // Multi-language
    defaultLanguage: 'fr',
    enableMultiLanguage: true,

    // Footer and Terms
    footerText: 'Merci pour votre confiance - شكرا لثقتكم',
    termsAndConditions: 'Conditions de vente disponibles sur demande.\nPaiement à 30 jours.\nTout retard de paiement entraînera des pénalités.',
    bankDetails: 'Banque: Attijariwafa Bank\nRIB: 007 123 ************* 34\nSWIFT: BCMAMAMC',

    // Signature
    enableSignature: false,
    signatureText: 'Signature et cachet'
  });

  const handleSave = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with actual API
      console.log('Saving invoice settings:', settings);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSaved(true);
      setTimeout(() => setSaved(false), 3000);
    } catch (error) {
      console.error('Error saving invoice settings:', error);
      alert('Erreur lors de l\'enregistrement. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof InvoiceSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) { // 2MB limit
        alert('La taille du logo doit être inférieure à 2MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setSettings(prev => ({
          ...prev,
          logo: result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const generateInvoiceNumber = () => {
    const { invoicePrefix, invoiceCounter } = settings;
    const year = new Date().getFullYear();
    return `${invoicePrefix}-${year}-${String(invoiceCounter).padStart(4, '0')}`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(settings.locale, {
      style: 'currency',
      currency: settings.currency
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    };

    if (settings.locale === 'fr-MA') {
      return date.toLocaleDateString('fr-FR', options);
    } else if (settings.locale === 'ar-MA') {
      return date.toLocaleDateString('ar-MA', options);
    }
    return date.toLocaleDateString('en-US', options);
  };

  const generateQRCode = (content: string) => {
    // In a real implementation, you would use a QR code library
    return `https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=${encodeURIComponent(content)}`;
  };

  const exportToPDF = () => {
    // In a real implementation, you would use a PDF library like jsPDF or react-pdf
    alert('Fonctionnalité d\'export PDF à implémenter');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Personnalisation des Factures</h1>
            <p className="text-gray-600">Configurez vos factures selon les normes marocaines</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setPreviewInvoice(true)}
            className="px-4 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50"
          >
            <Eye className="h-5 w-5 mr-2" />
            Aperçu
          </button>
          <button
            onClick={exportToPDF}
            className="px-4 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50"
          >
            <Download className="h-5 w-5 mr-2" />
            Export PDF
          </button>
          <button
            onClick={handleSave}
            disabled={loading}
            className={`px-4 py-2 rounded-lg flex items-center ${loading
              ? 'bg-gray-400 cursor-not-allowed'
              : saved
                ? 'bg-green-600 hover:bg-green-700'
                : 'bg-blue-600 hover:bg-blue-700'
              } text-white`}
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="h-5 w-5 mr-2" />
            )}
            {loading ? 'Enregistrement...' : saved ? 'Enregistré!' : 'Enregistrer'}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Settings Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Company Information */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Building className="h-6 w-6 text-blue-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Informations de l'Entreprise</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom de l'Entreprise *
                </label>
                <input
                  type="text"
                  value={settings.companyName}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Adresse Complète *
                </label>
                <textarea
                  rows={3}
                  value={settings.companyAddress}
                  onChange={(e) => handleInputChange('companyAddress', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="123 Avenue Mohammed V, Quartier Maarif&#10;20000 Casablanca, Maroc"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  ICE (Identifiant Commun de l'Entreprise) *
                </label>
                <input
                  type="text"
                  value={settings.ice}
                  onChange={(e) => handleInputChange('ice', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0*************3"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  RC (Registre de Commerce) *
                </label>
                <input
                  type="text"
                  value={settings.rc}
                  onChange={(e) => handleInputChange('rc', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="12345"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  CNSS (si applicable)
                </label>
                <input
                  type="text"
                  value={settings.cnss}
                  onChange={(e) => handleInputChange('cnss', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="1234567"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  IF (Identifiant Fiscal) *
                </label>
                <input
                  type="text"
                  value={settings.taxId}
                  onChange={(e) => handleInputChange('taxId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="**********"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Téléphone *
                </label>
                <input
                  type="tel"
                  value={settings.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="+*********** 456"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  value={settings.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Site Web
                </label>
                <input
                  type="url"
                  value={settings.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="www.electronicspro.ma"
                />
              </div>
            </div>
          </div>

          {/* Logo and Branding */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Image className="h-6 w-6 text-purple-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Logo et Image de Marque</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Logo de l'Entreprise
                </label>
                <div className="flex items-center space-x-4">
                  {settings.logo ? (
                    <div className="flex items-center space-x-4">
                      <img
                        src={settings.logo}
                        alt="Logo"
                        className="h-16 w-16 object-contain border border-gray-300 rounded"
                      />
                      <button
                        onClick={() => handleInputChange('logo', null)}
                        className="text-red-600 hover:text-red-700 text-sm"
                      >
                        Supprimer
                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-4">
                      <div className="h-16 w-16 border-2 border-dashed border-gray-300 rounded flex items-center justify-center">
                        <Image className="h-8 w-8 text-gray-400" />
                      </div>
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        className="px-4 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Télécharger Logo
                      </button>
                    </div>
                  )}
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Formats acceptés: PNG, JPG, SVG. Taille max: 2MB
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Position du Logo
                </label>
                <select
                  value={settings.logoPosition}
                  onChange={(e) => handleInputChange('logoPosition', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="left">Gauche</option>
                  <option value="center">Centre</option>
                  <option value="right">Droite</option>
                </select>
              </div>
            </div>
          </div>

          {/* Layout and Design */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Layout className="h-6 w-6 text-green-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Mise en Page et Design</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Modèle de Facture
                </label>
                <select
                  value={settings.template}
                  onChange={(e) => handleInputChange('template', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="simple">Simple</option>
                  <option value="compact">Compact</option>
                  <option value="signature">Avec Signature</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Taille de Police
                </label>
                <select
                  value={settings.fontSize}
                  onChange={(e) => handleInputChange('fontSize', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="small">Petite</option>
                  <option value="medium">Moyenne</option>
                  <option value="large">Grande</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Couleur Principale
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="color"
                    value={settings.primaryColor}
                    onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                    className="h-10 w-16 border border-gray-300 rounded cursor-pointer"
                  />
                  <input
                    type="text"
                    value={settings.primaryColor}
                    onChange={(e) => handleInputChange('primaryColor', e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Couleur Secondaire
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="color"
                    value={settings.secondaryColor}
                    onChange={(e) => handleInputChange('secondaryColor', e.target.value)}
                    className="h-10 w-16 border border-gray-300 rounded cursor-pointer"
                  />
                  <input
                    type="text"
                    value={settings.secondaryColor}
                    onChange={(e) => handleInputChange('secondaryColor', e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Formatting and Localization */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Globe className="h-6 w-6 text-orange-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Format et Localisation</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Devise
                </label>
                <select
                  value={settings.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="MAD">Dirham Marocain (MAD)</option>
                  <option value="USD">Dollar US (USD)</option>
                  <option value="EUR">Euro (EUR)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Locale
                </label>
                <select
                  value={settings.locale}
                  onChange={(e) => handleInputChange('locale', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="fr-MA">Français (Maroc)</option>
                  <option value="ar-MA">العربية (المغرب)</option>
                  <option value="en-US">English (US)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Format de Date
                </label>
                <select
                  value={settings.dateFormat}
                  onChange={(e) => handleInputChange('dateFormat', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                  <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                  <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Langue par Défaut
                </label>
                <select
                  value={settings.defaultLanguage}
                  onChange={(e) => handleInputChange('defaultLanguage', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="fr">Français</option>
                  <option value="ar">العربية</option>
                  <option value="en">English</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableMultiLanguage"
                    checked={settings.enableMultiLanguage}
                    onChange={(e) => handleInputChange('enableMultiLanguage', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="enableMultiLanguage" className="ml-2 text-sm text-gray-700">
                    Activer le support multi-langues
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Features and QR Code */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <QrCode className="h-6 w-6 text-indigo-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Fonctionnalités et QR Code</h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableQrCode"
                  checked={settings.enableQrCode}
                  onChange={(e) => handleInputChange('enableQrCode', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="enableQrCode" className="ml-2 text-sm text-gray-700">
                  Activer le QR Code sur les factures
                </label>
              </div>

              {settings.enableQrCode && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contenu du QR Code
                  </label>
                  <select
                    value={settings.qrCodeContent}
                    onChange={(e) => handleInputChange('qrCodeContent', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="invoice_url">URL de la facture</option>
                    <option value="company_info">Informations de l'entreprise</option>
                    <option value="payment_info">Informations de paiement</option>
                  </select>
                </div>
              )}

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoGenerateNumber"
                  checked={settings.autoGenerateNumber}
                  onChange={(e) => handleInputChange('autoGenerateNumber', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="autoGenerateNumber" className="ml-2 text-sm text-gray-700">
                  Génération automatique des numéros de facture
                </label>
              </div>

              {settings.autoGenerateNumber && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Préfixe
                    </label>
                    <input
                      type="text"
                      value={settings.invoicePrefix}
                      onChange={(e) => handleInputChange('invoicePrefix', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="FAC"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Compteur Actuel
                    </label>
                    <input
                      type="number"
                      value={settings.invoiceCounter}
                      onChange={(e) => handleInputChange('invoiceCounter', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      min="1"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer and Terms */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <FileText className="h-6 w-6 text-teal-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Pied de Page et Conditions</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Texte de Pied de Page
                </label>
                <textarea
                  rows={2}
                  value={settings.footerText}
                  onChange={(e) => handleInputChange('footerText', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Merci pour votre confiance - شكرا لثقتكم"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Conditions Générales de Vente
                </label>
                <textarea
                  rows={4}
                  value={settings.termsAndConditions}
                  onChange={(e) => handleInputChange('termsAndConditions', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Conditions de vente disponibles sur demande..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Coordonnées Bancaires
                </label>
                <textarea
                  rows={3}
                  value={settings.bankDetails}
                  onChange={(e) => handleInputChange('bankDetails', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Banque: Attijariwafa Bank&#10;RIB: 007 123 ************* 34&#10;SWIFT: BCMAMAMC"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableSignature"
                  checked={settings.enableSignature}
                  onChange={(e) => handleInputChange('enableSignature', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="enableSignature" className="ml-2 text-sm text-gray-700">
                  Inclure un espace pour signature
                </label>
              </div>

              {settings.enableSignature && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Texte de Signature
                  </label>
                  <input
                    type="text"
                    value={settings.signatureText}
                    onChange={(e) => handleInputChange('signatureText', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Signature et cachet"
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Preview Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow sticky top-6">
            <div className="flex items-center mb-4">
              <FileText className="h-6 w-6 text-blue-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Aperçu Facture</h3>
            </div>

            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 min-h-96">
              <div className="text-center text-gray-500 mt-20">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p>Aperçu de la facture</p>
                <p className="text-sm">Cliquez sur "Aperçu" pour voir le rendu</p>
              </div>
            </div>

            <div className="mt-4 space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Numéro:</span>
                <span className="font-medium">{generateInvoiceNumber()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date:</span>
                <span className="font-medium">{formatDate(new Date())}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Devise:</span>
                <span className="font-medium">{settings.currency}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Langue:</span>
                <span className="font-medium capitalize">{settings.defaultLanguage}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Invoice Preview Modal */}
      <InvoicePreview
        isOpen={previewInvoice}
        onClose={() => setPreviewInvoice(false)}
        settings={settings}
      />
    </div>
  );
}
