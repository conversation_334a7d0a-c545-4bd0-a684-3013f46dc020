'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Save,
  ArrowLeft,
  Search,
  Calculator,
  DollarSign,
  CreditCard,
  User,
  Receipt,
  TrendingUp
} from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  balance: number; // Current balance (positive = owes us, negative = we owe them)
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  amount: number;
  remainingAmount: number;
  date: string;
}

export default function NewPaymentPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [customerInvoices, setCustomerInvoices] = useState<Invoice[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [customerSearch, setCustomerSearch] = useState('');
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  
  const [paymentData, setPaymentData] = useState({
    paymentNumber: `PAY-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
    amount: 0,
    type: 'payment' as 'payment' | 'credit' | 'debt',
    method: 'cash' as 'cash' | 'card' | 'transfer' | 'check',
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: ''
  });

  useEffect(() => {
    // Mock customers data - replace with actual API call
    const mockCustomers: Customer[] = [
      {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        balance: 0 // Balanced
      },
      {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+1234567891',
        balance: -500 // We owe them $500
      },
      {
        id: '3',
        name: 'Bob Johnson',
        email: '<EMAIL>',
        phone: '+1234567892',
        balance: 1428.9 // They owe us $1428.9
      },
      {
        id: '4',
        name: 'Alice Brown',
        email: '<EMAIL>',
        phone: '+1234567893',
        balance: 250 // They owe us $250
      }
    ];
    setCustomers(mockCustomers);
  }, []);

  useEffect(() => {
    if (selectedCustomer) {
      // Mock invoices for selected customer
      const mockInvoices: Invoice[] = [
        {
          id: '1',
          invoiceNumber: 'INV-2024-001',
          amount: 1130.8,
          remainingAmount: 0,
          date: '2024-01-15'
        },
        {
          id: '2',
          invoiceNumber: 'INV-2024-003',
          amount: 1428.9,
          remainingAmount: 1428.9,
          date: '2024-01-10'
        }
      ].filter(invoice => {
        // Show invoices based on customer
        if (selectedCustomer.id === '3') return invoice.remainingAmount > 0;
        return false;
      });
      
      setCustomerInvoices(mockInvoices);
    } else {
      setCustomerInvoices([]);
    }
  }, [selectedCustomer]);

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
    customer.email.toLowerCase().includes(customerSearch.toLowerCase())
  );

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
    setCustomerSearch(customer.name);
    setShowCustomerDropdown(false);
    setSelectedInvoice(null);
    
    // Auto-fill amount based on customer balance and payment type
    if (paymentData.type === 'payment' && customer.balance > 0) {
      setPaymentData(prev => ({ ...prev, amount: customer.balance }));
    } else if (paymentData.type === 'credit' && customer.balance < 0) {
      setPaymentData(prev => ({ ...prev, amount: Math.abs(customer.balance) }));
    }
  };

  const handleInvoiceSelect = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setPaymentData(prev => ({
      ...prev,
      amount: invoice.remainingAmount,
      reference: invoice.invoiceNumber,
      description: `Payment for ${invoice.invoiceNumber}`
    }));
  };

  const handleTypeChange = (type: 'payment' | 'credit' | 'debt') => {
    setPaymentData(prev => ({ ...prev, type }));
    
    // Auto-generate payment number based on type
    const prefix = type === 'payment' ? 'PAY' : type === 'credit' ? 'CRD' : 'DBT';
    const newNumber = `${prefix}-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`;
    setPaymentData(prev => ({ ...prev, paymentNumber: newNumber }));
    
    // Auto-fill amount based on customer balance
    if (selectedCustomer) {
      if (type === 'payment' && selectedCustomer.balance > 0) {
        setPaymentData(prev => ({ ...prev, amount: selectedCustomer.balance }));
      } else if (type === 'credit' && selectedCustomer.balance < 0) {
        setPaymentData(prev => ({ ...prev, amount: Math.abs(selectedCustomer.balance) }));
      } else {
        setPaymentData(prev => ({ ...prev, amount: 0 }));
      }
    }
  };

  const calculateNewBalance = () => {
    if (!selectedCustomer) return 0;
    
    let newBalance = selectedCustomer.balance;
    
    if (paymentData.type === 'payment') {
      newBalance -= paymentData.amount;
    } else if (paymentData.type === 'credit') {
      newBalance -= paymentData.amount;
    } else if (paymentData.type === 'debt') {
      newBalance += paymentData.amount;
    }
    
    return newBalance;
  };

  const handleSave = async () => {
    if (!selectedCustomer) {
      alert('Please select a customer');
      return;
    }

    if (paymentData.amount <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    setLoading(true);
    
    try {
      // Mock API call - replace with actual API
      const paymentPayload = {
        ...paymentData,
        customer: selectedCustomer,
        invoice: selectedInvoice,
        newBalance: calculateNewBalance()
      };

      console.log('Saving payment:', paymentPayload);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Redirect to payments list
      router.push('/electronics/payments');
    } catch (error) {
      console.error('Error saving payment:', error);
      alert('Error saving payment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'cash': return <DollarSign className="h-5 w-5" />;
      case 'card': return <CreditCard className="h-5 w-5" />;
      case 'transfer': return <TrendingUp className="h-5 w-5" />;
      case 'check': return <Receipt className="h-5 w-5" />;
      default: return <DollarSign className="h-5 w-5" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Record Payment</h1>
            <p className="text-gray-600">Record a new payment, credit, or debt transaction</p>
          </div>
        </div>
        <button
          onClick={handleSave}
          disabled={loading}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-blue-700 disabled:opacity-50"
        >
          <Save className="h-5 w-5 mr-2" />
          {loading ? 'Saving...' : 'Save Payment'}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Payment Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Payment Details */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Number
                </label>
                <input
                  type="text"
                  value={paymentData.paymentNumber}
                  onChange={(e) => setPaymentData({...paymentData, paymentNumber: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <input
                  type="date"
                  value={paymentData.date}
                  onChange={(e) => setPaymentData({...paymentData, date: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Transaction Type
                </label>
                <select
                  value={paymentData.type}
                  onChange={(e) => handleTypeChange(e.target.value as 'payment' | 'credit' | 'debt')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="payment">Payment (Customer pays us)</option>
                  <option value="credit">Credit (We owe customer)</option>
                  <option value="debt">Debt (Customer owes us)</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Method
                </label>
                <select
                  value={paymentData.method}
                  onChange={(e) => setPaymentData({...paymentData, method: e.target.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="cash">Cash</option>
                  <option value="card">Credit/Debit Card</option>
                  <option value="transfer">Bank Transfer</option>
                  <option value="check">Check</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={paymentData.amount}
                  onChange={(e) => setPaymentData({...paymentData, amount: parseFloat(e.target.value) || 0})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reference (Optional)
                </label>
                <input
                  type="text"
                  value={paymentData.reference}
                  onChange={(e) => setPaymentData({...paymentData, reference: e.target.value})}
                  placeholder="Invoice number, check number, etc."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                rows={3}
                value={paymentData.description}
                onChange={(e) => setPaymentData({...paymentData, description: e.target.value})}
                placeholder="Description of the payment..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Customer Selection */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Customer
              </label>
              <div className="relative">
                <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search customers..."
                  value={customerSearch}
                  onChange={(e) => {
                    setCustomerSearch(e.target.value);
                    setShowCustomerDropdown(true);
                  }}
                  onFocus={() => setShowCustomerDropdown(true)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              {showCustomerDropdown && filteredCustomers.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {filteredCustomers.map((customer) => (
                    <button
                      key={customer.id}
                      onClick={() => handleCustomerSelect(customer)}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium text-gray-900">{customer.name}</div>
                          <div className="text-sm text-gray-500">{customer.email}</div>
                        </div>
                        <div className={`text-sm font-bold ${
                          customer.balance > 0 ? 'text-red-600' : customer.balance < 0 ? 'text-blue-600' : 'text-green-600'
                        }`}>
                          {customer.balance !== 0 && (
                            <span>
                              ${Math.abs(customer.balance).toFixed(2)}
                              {customer.balance > 0 ? ' owes' : ' credit'}
                            </span>
                          )}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {selectedCustomer && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{selectedCustomer.name}</h4>
                  <div className={`text-sm font-bold ${
                    selectedCustomer.balance > 0 ? 'text-red-600' : 
                    selectedCustomer.balance < 0 ? 'text-blue-600' : 'text-green-600'
                  }`}>
                    Current Balance: ${Math.abs(selectedCustomer.balance).toFixed(2)}
                    {selectedCustomer.balance > 0 ? ' (owes us)' : 
                     selectedCustomer.balance < 0 ? ' (we owe)' : ' (balanced)'}
                  </div>
                </div>
                <p className="text-sm text-gray-600">{selectedCustomer.email}</p>
                <p className="text-sm text-gray-600">{selectedCustomer.phone}</p>
              </div>
            )}
          </div>

          {/* Outstanding Invoices */}
          {selectedCustomer && customerInvoices.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Outstanding Invoices</h3>
              <div className="space-y-2">
                {customerInvoices.map((invoice) => (
                  <div
                    key={invoice.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedInvoice?.id === invoice.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleInvoiceSelect(invoice)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium text-gray-900">{invoice.invoiceNumber}</div>
                        <div className="text-sm text-gray-500">
                          Date: {new Date(invoice.date).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-red-600">
                          ${invoice.remainingAmount.toFixed(2)}
                        </div>
                        <div className="text-sm text-gray-500">
                          of ${invoice.amount.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Payment Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow sticky top-6">
            <div className="flex items-center mb-4">
              <Calculator className="h-5 w-5 text-blue-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Payment Summary</h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-center p-4 bg-gray-50 rounded-lg">
                {getMethodIcon(paymentData.method)}
                <span className="ml-2 font-medium capitalize">{paymentData.method}</span>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Transaction Type:</span>
                  <span className={`font-medium capitalize ${
                    paymentData.type === 'payment' ? 'text-green-600' :
                    paymentData.type === 'credit' ? 'text-blue-600' : 'text-red-600'
                  }`}>
                    {paymentData.type}
                  </span>
                </div>
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-bold text-lg">${paymentData.amount.toFixed(2)}</span>
                </div>
                
                {selectedCustomer && (
                  <>
                    <hr className="my-3" />
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Current Balance:</span>
                      <span className={`font-medium ${
                        selectedCustomer.balance > 0 ? 'text-red-600' : 
                        selectedCustomer.balance < 0 ? 'text-blue-600' : 'text-green-600'
                      }`}>
                        ${Math.abs(selectedCustomer.balance).toFixed(2)}
                      </span>
                    </div>
                    
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">New Balance:</span>
                      <span className={`font-bold ${
                        calculateNewBalance() > 0 ? 'text-red-600' : 
                        calculateNewBalance() < 0 ? 'text-blue-600' : 'text-green-600'
                      }`}>
                        ${Math.abs(calculateNewBalance()).toFixed(2)}
                        {calculateNewBalance() > 0 ? ' (owes)' : 
                         calculateNewBalance() < 0 ? ' (credit)' : ' (balanced)'}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={handleSave}
                disabled={loading || !selectedCustomer || paymentData.amount <= 0}
                className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Saving...' : 'Record Payment'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
