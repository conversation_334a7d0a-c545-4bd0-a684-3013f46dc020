import { Metadata } from 'next';
import RouteProtection from '@/components/RouteProtection';
import ElectronicsLayout from '@/components/electronics/ElectronicsLayout';

export const metadata: Metadata = {
  title: 'Electronics CRM - Moul Hanout',
  description: 'Electronics shop management system',
};

export default function ElectronicsLayoutWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <RouteProtection requiredBusinessModel="electronics">
      <ElectronicsLayout>
        {children}
      </ElectronicsLayout>
    </RouteProtection>
  );
}
