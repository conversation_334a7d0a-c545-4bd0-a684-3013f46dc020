'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, Loader2, Store, Globe, Mail, Phone, Smartphone } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    preferredLanguage: 'fr',
    businessModel: '', // Add business model selection
  });
  const [registrationType, setRegistrationType] = useState<'email' | 'phone'>('email');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const router = useRouter();
  const searchParams = useSearchParams();

  console.log('Register page rendered');

  // Pre-fill form data from URL parameters and localStorage
  useEffect(() => {
    const name = searchParams.get('name');
    const phone = searchParams.get('phone');
    const email = searchParams.get('email');

    // Get preferred business model from localStorage
    const preferredBusinessModel = localStorage.getItem('preferred-business-model');

    if (phone && !email) {
      setRegistrationType('phone');
      setFormData(prev => ({
        ...prev,
        phone,
        ...(preferredBusinessModel && { businessModel: preferredBusinessModel })
      }));
    } else if (email) {
      setRegistrationType('email');
      setFormData(prev => ({
        ...prev,
        email,
        ...(preferredBusinessModel && { businessModel: preferredBusinessModel })
      }));
    } else if (preferredBusinessModel) {
      setFormData(prev => ({
        ...prev,
        businessModel: preferredBusinessModel
      }));
    }
  }, [searchParams]);

  const handleInputChange = (field: string, value: string) => {
    console.log(`Register form field changed: ${field} = ${value}`);
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    if (error) setError(''); // Clear error when user starts typing
    if (success) setSuccess(''); // Clear success when user changes form
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Register form submitted');
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      console.log('Register response received:', data);

      if (!response.ok) {
        setError(data.error || 'Registration failed');
        console.error('Registration failed:', data.error);
        return;
      }

      console.log('Registration successful');
      setSuccess('Compte créé avec succès ! Redirection en cours...');

      // If user selected a business model during registration, save it and redirect
      if (formData.businessModel) {
        // Store the business model preference
        localStorage.setItem('preferred-business-model', formData.businessModel);

        // Auto-login the user and redirect to their chosen dashboard
        try {
          const loginResponse = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              identifier: formData.email || formData.phone,
              password: formData.password,
            }),
          });

          if (loginResponse.ok) {
            const loginData = await loginResponse.json();
            localStorage.setItem('auth-token', loginData.token);
            localStorage.setItem('user', JSON.stringify(loginData.user));

            // Save business model preference
            await fetch('/api/user/preferences', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${loginData.token}`
              },
              body: JSON.stringify({ businessModel: formData.businessModel })
            });

            // Clear the localStorage preference and redirect
            localStorage.removeItem('preferred-business-model');

            setTimeout(() => {
              const redirectUrl = formData.businessModel === 'electronics' ? '/electronics' : '/dashboard';
              router.push(redirectUrl);
            }, 1500);
          } else {
            // Auto-login failed, redirect to login page
            setTimeout(() => {
              router.push('/login');
            }, 2000);
          }
        } catch (loginError) {
          console.error('Auto-login failed:', loginError);
          setTimeout(() => {
            router.push('/login');
          }, 2000);
        }
      } else {
        // No business model selected, redirect to login
        setTimeout(() => {
          router.push('/login');
        }, 2000);
      }

    } catch (err) {
      console.error('Registration error:', err);
      setError('Erreur de connexion. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-amber-50 flex items-center justify-center p-4" data-macaly="register-container">
      <Card className="w-full max-w-md shadow-lg border-0 bg-white/95 backdrop-blur-sm" data-macaly="register-card">
        <CardHeader className="text-center space-y-4" data-macaly="register-header">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-amber-500 rounded-xl flex items-center justify-center">
              <Store className="w-8 h-8 text-white" />
            </div>
          </div>
          <div>
            <CardTitle className="text-2xl font-bold text-gray-900" data-macaly="register-title">
              Créer un compte
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2" data-macaly="register-subtitle">
              Démarrez la gestion de votre حانوت
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-6" data-macaly="register-form">
          {error && (
            <Alert variant="destructive" data-macaly="register-error">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="border-green-200 bg-green-50 text-green-800" data-macaly="register-success">
              <AlertCircle className="h-4 w-4 text-green-600" />
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Registration Type Tabs */}
            <Tabs value={registrationType} onValueChange={(value) => {
              setRegistrationType(value as 'email' | 'phone');
              setFormData(prev => ({ ...prev, email: '', phone: '' }));
              setError('');
            }}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="email" className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  Email
                </TabsTrigger>
                <TabsTrigger value="phone" className="flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  Téléphone
                </TabsTrigger>
              </TabsList>

              <TabsContent value="email" className="space-y-2 mt-4">
                <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Adresse email
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  disabled={loading}
                  required={registrationType === 'email'}
                  data-macaly="email-input"
                />
              </TabsContent>

              <TabsContent value="phone" className="space-y-2 mt-4">
                <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                  رقم الهاتف / Numéro de téléphone
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="06 XX XX XX XX"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  disabled={loading}
                  required={registrationType === 'phone'}
                  data-macaly="phone-input"
                />
                <p className="text-xs text-gray-500">
                  Format accepté: 06XXXXXXXX ou +212XXXXXXXXX
                </p>
              </TabsContent>
            </Tabs>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                Mot de passe
              </Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                disabled={loading}
                required
                data-macaly="password-input"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                Confirmer le mot de passe
              </Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="••••••••"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                disabled={loading}
                required
                data-macaly="confirm-password-input"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="language" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Globe className="w-4 h-4" />
                Langue préférée
              </Label>
              <Select
                value={formData.preferredLanguage}
                onValueChange={(value) => handleInputChange('preferredLanguage', value)}
                disabled={loading}
              >
                <SelectTrigger className="h-11" data-macaly="language-select">
                  <SelectValue placeholder="Choisissez votre langue" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fr">🇫🇷 Français</SelectItem>
                  <SelectItem value="ar">🇲🇦 العربية</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Business Model Selection */}
            <div className="space-y-2">
              <Label htmlFor="businessModel" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Store className="w-4 h-4" />
                نوع التجارة / Type de commerce
              </Label>
              <Select
                value={formData.businessModel}
                onValueChange={(value) => handleInputChange('businessModel', value)}
                disabled={loading}
              >
                <SelectTrigger className="h-11" data-macaly="business-model-select">
                  <SelectValue placeholder="اختر نوع التجارة / Choisir le type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">
                    <div className="flex items-center gap-2">
                      <Store className="w-4 h-4 text-green-600" />
                      <span>حانوت عام / Commerce Général</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="electronics">
                    <div className="flex items-center gap-2">
                      <Smartphone className="w-4 h-4 text-blue-600" />
                      <span>محل الإلكترونيات / Magasin d'Électronique</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                يمكنك تغيير هذا الاختيار لاحقا / Vous pouvez changer ce choix plus tard
              </p>
            </div>

            <Button
              type="submit"
              className="w-full h-11 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium transition-all duration-200"
              disabled={loading}
              data-macaly="register-button"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Création en cours...
                </>
              ) : (
                'Créer mon compte'
              )}
            </Button>
          </form>

          <div className="text-center pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              Déjà un compte ?{' '}
              <Link
                href="/login"
                className="font-medium text-blue-600 hover:text-blue-700 transition-colors"
                data-macaly="login-link"
              >
                Se connecter
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}