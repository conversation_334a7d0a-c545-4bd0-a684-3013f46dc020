'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import SalesAnalytics from '@/components/SalesAnalytics';
import Navigation from '@/components/Navigation';
import { getMessages, type Locale, getLocaleFromString } from '@/lib/i18n';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  Activity,
  RefreshCw
} from 'lucide-react';

interface AnalyticsStats {
  totalRevenue: number;
  totalSales: number;
  averageOrderValue: number;
  topSellingProduct: string;
  revenueGrowth: number;
  salesGrowth: number;
  customerGrowth: number;
  productsSold: number;
}

export default function AnalyticsPage() {
  const [locale, setLocale] = useState<Locale>('fr');
  const [stats, setStats] = useState<AnalyticsStats>({
    totalRevenue: 0,
    totalSales: 0,
    averageOrderValue: 0,
    topSellingProduct: '',
    revenueGrowth: 0,
    salesGrowth: 0,
    customerGrowth: 0,
    productsSold: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('30');
  const [refreshing, setRefreshing] = useState(false);
  const router = useRouter();

  const messages = getMessages(locale);

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem('auth-token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      console.log('No authentication found, redirecting to login');
      router.push('/login');
      return;
    }

    // Set user's preferred language
    const user = JSON.parse(userData);
    setLocale(getLocaleFromString(user.preferredLanguage));

    fetchAnalyticsStats();
  }, [router, selectedPeriod]);

  const fetchAnalyticsStats = async () => {
    try {
      const token = localStorage.getItem('auth-token');

      const response = await fetch(`/api/analytics?period=${selectedPeriod}`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.log('Token expired, redirecting to login');
          localStorage.removeItem('auth-token');
          localStorage.removeItem('user');
          router.push('/login');
          return;
        }
        throw new Error('Failed to fetch analytics stats');
      }

      const data = await response.json();
      setStats(data.stats || {});
    } catch (err) {
      console.error('Analytics stats fetch error:', err);
      setError('Erreur lors du chargement des statistiques');

      // Generate mock data for development
      setStats({
        totalRevenue: 127500,
        totalSales: 1247,
        averageOrderValue: 102.3,
        topSellingProduct: 'Coca-Cola 1.5L',
        revenueGrowth: 12.5,
        salesGrowth: 8.3,
        customerGrowth: 15.2,
        productsSold: 3420
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAnalyticsStats();
  };

  const handleLocaleChange = (newLocale: Locale) => {
    console.log('Language changed to:', newLocale);
    setLocale(newLocale);

    const userData = localStorage.getItem('user');
    if (userData) {
      const user = JSON.parse(userData);
      user.preferredLanguage = newLocale;
      localStorage.setItem('user', JSON.stringify(user));
    }
  };

  const formatCurrency = (amount: number) => {
    const currencySymbol = locale === 'ar' ? 'درهم' : 'MAD';
    return `${amount.toLocaleString('fr-FR')} ${currencySymbol}`;
  };

  const StatCard = ({
    title,
    value,
    icon: Icon,
    trend,
    trendValue,
    color = 'blue',
    format = 'number'
  }: {
    title: string;
    value: number | string;
    icon: any;
    trend?: 'up' | 'down';
    trendValue?: string;
    color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple';
    format?: 'number' | 'currency' | 'text';
  }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      red: 'bg-red-50 text-red-600',
      yellow: 'bg-yellow-50 text-yellow-600',
      purple: 'bg-purple-50 text-purple-600'
    };

    const formatValue = (val: number | string) => {
      if (format === 'currency' && typeof val === 'number') return formatCurrency(val);
      if (format === 'number' && typeof val === 'number') return val.toLocaleString('fr-FR');
      return val;
    };

    return (
      <Card className="transition-all duration-200 hover:shadow-md">
        <CardContent className="p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-600 truncate">{title}</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">
                {formatValue(value)}
              </p>
              {trend && trendValue && (
                <div className="flex items-center mt-2">
                  {trend === 'up' ? (
                    <TrendingUp className="w-4 h-4 mr-1 text-green-500" />
                  ) : (
                    <TrendingDown className="w-4 h-4 mr-1 text-red-500" />
                  )}
                  <span className={`text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                    {trendValue}
                  </span>
                </div>
              )}
            </div>
            <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-lg flex items-center justify-center ${colorClasses[color]} flex-shrink-0 ml-4`}>
              <Icon className="w-5 h-5 sm:w-6 sm:h-6" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="flex h-screen">
        <Navigation locale={locale} onLocaleChange={handleLocaleChange} />
        <div className="flex-1 lg:ml-64 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des analyses...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50" data-macaly="analytics">
      <Navigation locale={locale} onLocaleChange={handleLocaleChange} />

      {/* Main Content */}
      <div className="flex-1 lg:ml-64" data-macaly="analytics-main">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 sm:px-6 py-6 sm:py-8">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900" data-macaly="analytics-title">
                Analyses et Statistiques
              </h1>
              <p className="text-gray-600 mt-1">
                Insights détaillés sur vos performances commerciales
              </p>
            </div>

            <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
              {/* Period Selector */}
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">7 jours</SelectItem>
                    <SelectItem value="30">30 jours</SelectItem>
                    <SelectItem value="90">3 mois</SelectItem>
                    <SelectItem value="365">1 an</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Refresh Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
                className="gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                <span className="hidden sm:inline">Actualiser</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6 space-y-6 sm:space-y-8">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            <StatCard
              title="Chiffre d'affaires"
              value={stats.totalRevenue}
              icon={DollarSign}
              color="green"
              format="currency"
              trend="up"
              trendValue={stats.revenueGrowth ? `+${stats.revenueGrowth}%` : '+0%'}
            />
            <StatCard
              title="Nombre de ventes"
              value={stats.totalSales}
              icon={ShoppingCart}
              color="blue"
              trend="up"
              trendValue={stats.salesGrowth ? `+${stats.salesGrowth}%` : '+0%'}
            />
            <StatCard
              title="Panier moyen"
              value={stats.averageOrderValue}
              icon={TrendingUp}
              color="purple"
              format="currency"
            />
            <StatCard
              title="Produits vendus"
              value={stats.productsSold}
              icon={Package}
              color="yellow"
            />
          </div>

          {/* Additional Metrics */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            <StatCard
              title="Croissance clients"
              value={stats.customerGrowth ? `+${stats.customerGrowth}%` : '+0%'}
              icon={Users}
              color="green"
              format="text"
            />
            <StatCard
              title="Produit le plus vendu"
              value={stats.topSellingProduct}
              icon={Activity}
              color="blue"
              format="text"
            />
            <Card className="transition-all duration-200 hover:shadow-md">
              <CardContent className="p-4 sm:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Période d'analyse</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">
                      {selectedPeriod} jours
                    </p>
                  </div>
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-50 text-gray-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-4">
                    <Calendar className="w-5 h-5 sm:w-6 sm:h-6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Analyses détaillées</h2>
              <Badge variant="outline" className="text-sm self-start sm:self-auto">
                Derniers {selectedPeriod} jours
              </Badge>
            </div>

            <SalesAnalytics className="w-full" locale={locale} />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-600">{error}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
