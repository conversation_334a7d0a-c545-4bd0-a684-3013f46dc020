import { NextRequest } from 'next/server';
import connectDB from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import UserPreferences from '@/models/UserPreferences';

export async function getUserRedirectUrl(request: NextRequest): Promise<string | null> {
  try {
    // Check if user is authenticated
    const user = getUserFromRequest(request);
    if (!user) {
      return null;
    }

    await connectDB();

    // Get user preferences
    const preferences = await UserPreferences.findOne({ userId: user.id });
    
    if (!preferences || !preferences.businessModel) {
      // User hasn't chosen a business model yet, stay on landing page
      return null;
    }

    // Return redirect URL based on business model
    return preferences.getRedirectUrl();

  } catch (error) {
    console.error('Error getting user redirect URL:', error);
    return null;
  }
}

export async function setUserBusinessModel(userId: string, businessModel: 'general' | 'electronics'): Promise<boolean> {
  try {
    await connectDB();

    let preferences = await UserPreferences.findOne({ userId });
    
    if (!preferences) {
      preferences = new UserPreferences({
        userId,
        businessModel,
        onboardingCompleted: true
      });
    } else {
      preferences.businessModel = businessModel;
      preferences.onboardingCompleted = true;
      preferences.lastLogin = new Date();
    }

    await preferences.save();
    return true;

  } catch (error) {
    console.error('Error setting user business model:', error);
    return false;
  }
}
