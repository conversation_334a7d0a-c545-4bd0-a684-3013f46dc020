'use client';

import { useState, useEffect } from 'react';
import {
  Plus,
  Shield,
  Calendar,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface Warranty {
  id: string;
  warrantyNumber: string;
  customerName: string;
  customerPhone: string;
  productName: string;
  serialNumber: string;
  purchaseDate: string;
  warrantyPeriod: number; // in months
  expiryDate: string;
  status: 'active' | 'expired' | 'claimed';
  claimDate?: string;
  claimReason?: string;
}

export default function WarrantyPage() {
  const [warranties, setWarranties] = useState<Warranty[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState('all');

  useEffect(() => {
    // Simulate loading warranty data
    setTimeout(() => {
      setWarranties([
        {
          id: '1',
          warrantyNumber: 'WAR-2024-001',
          customerName: '<PERSON>',
          customerPhone: '+212612345678',
          productName: 'iPhone 15 Pro',
          serialNumber: 'F2LW48XHQM',
          purchaseDate: '2024-01-15T10:30:00Z',
          warrantyPeriod: 12,
          expiryDate: '2025-01-15T10:30:00Z',
          status: 'active'
        },
        {
          id: '2',
          warrantyNumber: 'WAR-2024-002',
          customerName: 'Fatima Zahra',
          customerPhone: '+212687654321',
          productName: 'Samsung Galaxy S24',
          serialNumber: 'R58NA0BXKQH',
          purchaseDate: '2024-01-10T14:20:00Z',
          warrantyPeriod: 24,
          expiryDate: '2026-01-10T14:20:00Z',
          status: 'active'
        },
        {
          id: '3',
          warrantyNumber: 'WAR-2023-045',
          customerName: 'Omar Benali',
          customerPhone: '+212698765432',
          productName: 'MacBook Air',
          serialNumber: 'FVFXJ3Q1Q6L7',
          purchaseDate: '2023-01-20T11:15:00Z',
          warrantyPeriod: 12,
          expiryDate: '2024-01-20T11:15:00Z',
          status: 'expired'
        },
        {
          id: '4',
          warrantyNumber: 'WAR-2023-032',
          customerName: 'Laila Amrani',
          customerPhone: '+212654321987',
          productName: 'Dell XPS 13',
          serialNumber: 'BXPSC13',
          purchaseDate: '2023-06-15T09:00:00Z',
          warrantyPeriod: 36,
          expiryDate: '2026-06-15T09:00:00Z',
          status: 'claimed',
          claimDate: '2024-01-10T15:30:00Z',
          claimReason: 'Battery replacement'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredWarranties = warranties.filter(warranty =>
    selectedStatus === 'all' || warranty.status === selectedStatus
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'claimed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Shield className="h-4 w-4" />;
      case 'expired': return <AlertTriangle className="h-4 w-4" />;
      case 'claimed': return <CheckCircle className="h-4 w-4" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  };

  const statusOptions = [
    { value: 'all', label: 'All Warranties' },
    { value: 'active', label: 'Active' },
    { value: 'expired', label: 'Expired' },
    { value: 'claimed', label: 'Claimed' }
  ];

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Warranty Tracking</h1>
          <p className="text-gray-600">Track and manage product warranties</p>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-blue-700">
          <Plus className="h-5 w-5 mr-2" />
          Register Warranty
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Shield className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Warranties</p>
              <p className="text-2xl font-bold text-gray-900">{warranties.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active</p>
              <p className="text-2xl font-bold text-gray-900">
                {warranties.filter(w => w.status === 'active').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-yellow-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
              <p className="text-2xl font-bold text-gray-900">
                {warranties.filter(w => w.status === 'active' && isExpiringSoon(w.expiryDate)).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="h-8 w-8 bg-purple-500 rounded flex items-center justify-center">
              <span className="text-white font-bold">C</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Claims</p>
              <p className="text-2xl font-bold text-gray-900">
                {warranties.filter(w => w.status === 'claimed').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="flex items-center gap-4">
          <label className="text-sm font-medium text-gray-700">Filter by status:</label>
          <select
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Warranties Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Warranty #
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Purchase Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expiry Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  </td>
                </tr>
              ) : filteredWarranties.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                    No warranties found
                  </td>
                </tr>
              ) : (
                filteredWarranties.map((warranty) => (
                  <tr key={warranty.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{warranty.warrantyNumber}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{warranty.customerName}</div>
                        <div className="text-sm text-gray-500">{warranty.customerPhone}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{warranty.productName}</div>
                        <div className="text-sm text-gray-500">S/N: {warranty.serialNumber}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(warranty.purchaseDate).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {new Date(warranty.expiryDate).toLocaleDateString()}
                      </div>
                      {warranty.status === 'active' && isExpiringSoon(warranty.expiryDate) && (
                        <div className="text-xs text-yellow-600 flex items-center mt-1">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          Expires soon
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(warranty.status)}`}>
                        {getStatusIcon(warranty.status)}
                        <span className="ml-1">{warranty.status}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">View</button>
                        {warranty.status === 'active' && (
                          <button className="text-orange-600 hover:text-orange-900">Claim</button>
                        )}
                        <button className="text-green-600 hover:text-green-900">Print</button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
