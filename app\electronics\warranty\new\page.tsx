'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Save,
  ArrowLeft,
  Shield,
  Search,
  Calendar,
  User,
  Package,
  FileText,
  AlertCircle
} from 'lucide-react';

interface Product {
  id: string;
  name: string;
  sku: string;
  brand: string;
  category: string;
  warrantyPeriod: string;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  date: string;
  products: Array<{
    productId: string;
    productName: string;
    quantity: number;
    serialNumber?: string;
  }>;
}

interface WarrantyData {
  warrantyNumber: string;
  customerId: string;
  productId: string;
  invoiceId: string;
  serialNumber: string;
  purchaseDate: string;
  warrantyStartDate: string;
  warrantyEndDate: string;
  warrantyType: 'manufacturer' | 'extended' | 'store';
  warrantyPeriod: string;
  terms: string;
  notes: string;
  status: 'active' | 'expired' | 'claimed' | 'void';
}

export default function NewWarrantyPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [customerInvoices, setCustomerInvoices] = useState<Invoice[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [customerSearch, setCustomerSearch] = useState('');
  const [productSearch, setProductSearch] = useState('');
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [showProductDropdown, setShowProductDropdown] = useState(false);
  
  const [warrantyData, setWarrantyData] = useState<WarrantyData>({
    warrantyNumber: `WAR-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
    customerId: '',
    productId: '',
    invoiceId: '',
    serialNumber: '',
    purchaseDate: new Date().toISOString().split('T')[0],
    warrantyStartDate: new Date().toISOString().split('T')[0],
    warrantyEndDate: '',
    warrantyType: 'manufacturer',
    warrantyPeriod: '',
    terms: '',
    notes: '',
    status: 'active'
  });

  useEffect(() => {
    // Mock data - replace with actual API calls
    const mockProducts: Product[] = [
      {
        id: '1',
        name: 'iPhone 15 Pro',
        sku: 'IPH15P-128',
        brand: 'Apple',
        category: 'Smartphones',
        warrantyPeriod: '1 year'
      },
      {
        id: '2',
        name: 'Samsung Galaxy S24',
        sku: 'SGS24-256',
        brand: 'Samsung',
        category: 'Smartphones',
        warrantyPeriod: '2 years'
      },
      {
        id: '3',
        name: 'MacBook Pro 14"',
        sku: 'MBP14-512',
        brand: 'Apple',
        category: 'Laptops',
        warrantyPeriod: '1 year'
      }
    ];

    const mockCustomers: Customer[] = [
      {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890'
      },
      {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+1234567891'
      }
    ];

    setProducts(mockProducts);
    setCustomers(mockCustomers);
  }, []);

  useEffect(() => {
    if (selectedCustomer) {
      // Mock invoices for selected customer
      const mockInvoices: Invoice[] = [
        {
          id: '1',
          invoiceNumber: 'INV-2024-001',
          date: '2024-01-15',
          products: [
            {
              productId: '1',
              productName: 'iPhone 15 Pro',
              quantity: 1,
              serialNumber: 'IPH123456789'
            }
          ]
        },
        {
          id: '2',
          invoiceNumber: 'INV-2024-002',
          date: '2024-01-20',
          products: [
            {
              productId: '2',
              productName: 'Samsung Galaxy S24',
              quantity: 1,
              serialNumber: 'SGS987654321'
            }
          ]
        }
      ];
      
      setCustomerInvoices(mockInvoices);
    } else {
      setCustomerInvoices([]);
    }
  }, [selectedCustomer]);

  useEffect(() => {
    // Calculate warranty end date when start date or period changes
    if (warrantyData.warrantyStartDate && warrantyData.warrantyPeriod) {
      const startDate = new Date(warrantyData.warrantyStartDate);
      let endDate = new Date(startDate);
      
      // Parse warranty period (e.g., "1 year", "6 months", "90 days")
      const period = warrantyData.warrantyPeriod.toLowerCase();
      if (period.includes('year')) {
        const years = parseInt(period.match(/\d+/)?.[0] || '1');
        endDate.setFullYear(endDate.getFullYear() + years);
      } else if (period.includes('month')) {
        const months = parseInt(period.match(/\d+/)?.[0] || '1');
        endDate.setMonth(endDate.getMonth() + months);
      } else if (period.includes('day')) {
        const days = parseInt(period.match(/\d+/)?.[0] || '1');
        endDate.setDate(endDate.getDate() + days);
      }
      
      setWarrantyData(prev => ({
        ...prev,
        warrantyEndDate: endDate.toISOString().split('T')[0]
      }));
    }
  }, [warrantyData.warrantyStartDate, warrantyData.warrantyPeriod]);

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
    customer.email.toLowerCase().includes(customerSearch.toLowerCase())
  );

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(productSearch.toLowerCase()) ||
    product.sku.toLowerCase().includes(productSearch.toLowerCase()) ||
    product.brand.toLowerCase().includes(productSearch.toLowerCase())
  );

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
    setCustomerSearch(customer.name);
    setShowCustomerDropdown(false);
    setWarrantyData(prev => ({ ...prev, customerId: customer.id }));
    setSelectedInvoice(null);
    setSelectedProduct(null);
  };

  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product);
    setProductSearch(product.name);
    setShowProductDropdown(false);
    setWarrantyData(prev => ({ 
      ...prev, 
      productId: product.id,
      warrantyPeriod: product.warrantyPeriod
    }));
  };

  const handleInvoiceSelect = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setWarrantyData(prev => ({ 
      ...prev, 
      invoiceId: invoice.id,
      purchaseDate: invoice.date,
      warrantyStartDate: invoice.date
    }));
    
    // Auto-fill serial number if available
    const productInInvoice = invoice.products.find(p => p.productId === selectedProduct?.id);
    if (productInInvoice?.serialNumber) {
      setWarrantyData(prev => ({ 
        ...prev, 
        serialNumber: productInInvoice.serialNumber || ''
      }));
    }
  };

  const handleSave = async () => {
    // Validation
    if (!selectedCustomer) {
      alert('Please select a customer');
      return;
    }
    
    if (!selectedProduct) {
      alert('Please select a product');
      return;
    }
    
    if (!warrantyData.serialNumber.trim()) {
      alert('Serial number is required');
      return;
    }

    setLoading(true);
    
    try {
      // Mock API call - replace with actual API
      const warrantyPayload = {
        ...warrantyData,
        customer: selectedCustomer,
        product: selectedProduct,
        invoice: selectedInvoice,
        createdAt: new Date().toISOString()
      };

      console.log('Saving warranty:', warrantyPayload);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Redirect to warranty list
      router.push('/electronics/warranty');
    } catch (error) {
      console.error('Error saving warranty:', error);
      alert('Error saving warranty. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const warrantyTypes = [
    { value: 'manufacturer', label: 'Manufacturer Warranty' },
    { value: 'extended', label: 'Extended Warranty' },
    { value: 'store', label: 'Store Warranty' }
  ];

  const warrantyPeriods = [
    '90 days',
    '6 months',
    '1 year',
    '2 years',
    '3 years',
    '5 years'
  ];

  const isWarrantyExpired = () => {
    if (!warrantyData.warrantyEndDate) return false;
    return new Date(warrantyData.warrantyEndDate) < new Date();
  };

  const getDaysRemaining = () => {
    if (!warrantyData.warrantyEndDate) return 0;
    const endDate = new Date(warrantyData.warrantyEndDate);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Register New Warranty</h1>
            <p className="text-gray-600">Register a warranty for a purchased product</p>
          </div>
        </div>
        <button
          onClick={handleSave}
          disabled={loading}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-blue-700 disabled:opacity-50"
        >
          <Save className="h-5 w-5 mr-2" />
          {loading ? 'Saving...' : 'Register Warranty'}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Warranty Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Warranty Details */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Shield className="h-5 w-5 text-blue-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Warranty Details</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Warranty Number
                </label>
                <input
                  type="text"
                  value={warrantyData.warrantyNumber}
                  onChange={(e) => setWarrantyData({...warrantyData, warrantyNumber: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Warranty Type
                </label>
                <select
                  value={warrantyData.warrantyType}
                  onChange={(e) => setWarrantyData({...warrantyData, warrantyType: e.target.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {warrantyTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Warranty Period
                </label>
                <select
                  value={warrantyData.warrantyPeriod}
                  onChange={(e) => setWarrantyData({...warrantyData, warrantyPeriod: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select period</option>
                  {warrantyPeriods.map(period => (
                    <option key={period} value={period}>{period}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={warrantyData.status}
                  onChange={(e) => setWarrantyData({...warrantyData, status: e.target.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="active">Active</option>
                  <option value="expired">Expired</option>
                  <option value="claimed">Claimed</option>
                  <option value="void">Void</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Warranty Start Date
                </label>
                <input
                  type="date"
                  value={warrantyData.warrantyStartDate}
                  onChange={(e) => setWarrantyData({...warrantyData, warrantyStartDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Warranty End Date
                </label>
                <input
                  type="date"
                  value={warrantyData.warrantyEndDate}
                  onChange={(e) => setWarrantyData({...warrantyData, warrantyEndDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Customer Selection */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <User className="h-5 w-5 text-green-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Customer Information</h3>
            </div>
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Customer *
              </label>
              <div className="relative">
                <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search customers..."
                  value={customerSearch}
                  onChange={(e) => {
                    setCustomerSearch(e.target.value);
                    setShowCustomerDropdown(true);
                  }}
                  onFocus={() => setShowCustomerDropdown(true)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              {showCustomerDropdown && filteredCustomers.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {filteredCustomers.map((customer) => (
                    <button
                      key={customer.id}
                      onClick={() => handleCustomerSelect(customer)}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
                    >
                      <div className="font-medium text-gray-900">{customer.name}</div>
                      <div className="text-sm text-gray-500">{customer.email}</div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Product Selection */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Package className="h-5 w-5 text-purple-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Product Information</h3>
            </div>
            <div className="relative mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Product *
              </label>
              <div className="relative">
                <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={productSearch}
                  onChange={(e) => {
                    setProductSearch(e.target.value);
                    setShowProductDropdown(true);
                  }}
                  onFocus={() => setShowProductDropdown(true)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              {showProductDropdown && filteredProducts.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {filteredProducts.map((product) => (
                    <button
                      key={product.id}
                      onClick={() => handleProductSelect(product)}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
                    >
                      <div className="font-medium text-gray-900">{product.name}</div>
                      <div className="text-sm text-gray-500">{product.brand} • {product.sku}</div>
                    </button>
                  ))}
                </div>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Serial Number *
              </label>
              <input
                type="text"
                value={warrantyData.serialNumber}
                onChange={(e) => setWarrantyData({...warrantyData, serialNumber: e.target.value})}
                placeholder="Enter product serial number"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Invoice Selection */}
          {selectedCustomer && customerInvoices.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center mb-4">
                <FileText className="h-5 w-5 text-orange-500 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Purchase Invoice</h3>
              </div>
              <div className="space-y-2">
                {customerInvoices.map((invoice) => (
                  <div
                    key={invoice.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedInvoice?.id === invoice.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleInvoiceSelect(invoice)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium text-gray-900">{invoice.invoiceNumber}</div>
                        <div className="text-sm text-gray-500">
                          Date: {new Date(invoice.date).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        {invoice.products.length} product(s)
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Terms and Notes */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Terms and Notes</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Warranty Terms
                </label>
                <textarea
                  rows={3}
                  value={warrantyData.terms}
                  onChange={(e) => setWarrantyData({...warrantyData, terms: e.target.value})}
                  placeholder="Warranty terms and conditions..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Additional Notes
                </label>
                <textarea
                  rows={3}
                  value={warrantyData.notes}
                  onChange={(e) => setWarrantyData({...warrantyData, notes: e.target.value})}
                  placeholder="Any additional notes..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Warranty Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow sticky top-6">
            <div className="flex items-center mb-4">
              <Shield className="h-5 w-5 text-blue-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Warranty Summary</h3>
            </div>
            
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Warranty Number</div>
                <div className="font-medium text-gray-900">{warrantyData.warrantyNumber}</div>
              </div>
              
              {selectedCustomer && (
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Customer</div>
                  <div className="font-medium text-gray-900">{selectedCustomer.name}</div>
                  <div className="text-sm text-gray-600">{selectedCustomer.email}</div>
                </div>
              )}
              
              {selectedProduct && (
                <div className="p-4 bg-purple-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Product</div>
                  <div className="font-medium text-gray-900">{selectedProduct.name}</div>
                  <div className="text-sm text-gray-600">{selectedProduct.brand} • {selectedProduct.sku}</div>
                </div>
              )}
              
              {warrantyData.serialNumber && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Serial Number</div>
                  <div className="font-medium text-gray-900">{warrantyData.serialNumber}</div>
                </div>
              )}
              
              {warrantyData.warrantyPeriod && (
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Warranty Period</div>
                  <div className="font-medium text-green-600">{warrantyData.warrantyPeriod}</div>
                </div>
              )}
              
              {warrantyData.warrantyEndDate && (
                <div className={`p-4 rounded-lg ${
                  isWarrantyExpired() ? 'bg-red-50' : 'bg-yellow-50'
                }`}>
                  <div className="text-sm text-gray-600 mb-1">Warranty Status</div>
                  <div className={`font-medium ${
                    isWarrantyExpired() ? 'text-red-600' : 'text-yellow-600'
                  }`}>
                    {isWarrantyExpired() ? 'Expired' : `${getDaysRemaining()} days remaining`}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    Expires: {new Date(warrantyData.warrantyEndDate).toLocaleDateString()}
                  </div>
                </div>
              )}
              
              {selectedInvoice && (
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Purchase Invoice</div>
                  <div className="font-medium text-orange-600">{selectedInvoice.invoiceNumber}</div>
                  <div className="text-sm text-gray-600">
                    {new Date(selectedInvoice.date).toLocaleDateString()}
                  </div>
                </div>
              )}
            </div>

            {isWarrantyExpired() && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                  <span className="text-sm text-red-700">This warranty has expired</span>
                </div>
              </div>
            )}

            <div className="mt-6">
              <button
                onClick={handleSave}
                disabled={loading || !selectedCustomer || !selectedProduct || !warrantyData.serialNumber.trim()}
                className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Saving...' : 'Register Warranty'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
