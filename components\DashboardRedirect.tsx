'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

interface DashboardRedirectProps {
  children: React.ReactNode;
}

export default function DashboardRedirect({ children }: DashboardRedirectProps) {
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    checkUserPreferences();
  }, []);

  const checkUserPreferences = async () => {
    try {
      const token = localStorage.getItem('auth-token');

      if (!token) {
        // User not authenticated, show landing page
        setShouldRender(true);
        setIsChecking(false);
        return;
      }

      // User is authenticated, check their business model preference
      const response = await fetch('/api/user/preferences', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('DashboardRedirect - User preferences:', data.preferences);

        if (data.preferences.businessModel) {
          // User has a business model preference, redirect them
          console.log('DashboardRedirect - Redirecting to:', data.preferences.redirectUrl);
          router.push(data.preferences.redirectUrl);
          return;
        } else {
          // User doesn't have a business model preference, show landing page to choose
          console.log('DashboardRedirect - No business model, showing landing page');
          setShouldRender(true);
        }
      } else {
        // Error fetching preferences, show landing page
        console.log('DashboardRedirect - Error fetching preferences, showing landing page');
        setShouldRender(true);
      }
    } catch (error) {
      console.error('Error checking user preferences:', error);
      setShouldRender(true);
    } finally {
      setIsChecking(false);
    }
  };

  if (isChecking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 font-medium">جاري التحميل...</p>
          <p className="text-sm text-gray-500">Chargement en cours...</p>
        </div>
      </div>
    );
  }

  if (!shouldRender) {
    return null;
  }

  return <>{children}</>;
}
