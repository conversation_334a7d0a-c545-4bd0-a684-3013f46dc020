import mongoose, { Document, Schema } from 'mongoose';

export interface IElectronicsCustomer extends Document {
  userId: mongoose.Types.ObjectId;
  name: string;
  phone: string;
  email?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  customerType: 'new' | 'regular' | 'vip';
  totalPurchases: number;
  totalSpent: number;
  outstandingCredit: number;
  lastVisit: Date;
  firstVisit: Date;
  notes?: string;
  preferences?: {
    preferredBrands?: string[];
    preferredCategories?: string[];
    communicationMethod?: 'phone' | 'email' | 'whatsapp';
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ElectronicsCustomerSchema = new Schema<IElectronicsCustomer>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  phone: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    trim: true,
    lowercase: true
  },
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String
  },
  customerType: {
    type: String,
    enum: ['new', 'regular', 'vip'],
    default: 'new'
  },
  totalPurchases: {
    type: Number,
    default: 0,
    min: 0
  },
  totalSpent: {
    type: Number,
    default: 0,
    min: 0
  },
  outstandingCredit: {
    type: Number,
    default: 0,
    min: 0
  },
  lastVisit: {
    type: Date,
    default: Date.now
  },
  firstVisit: {
    type: Date,
    default: Date.now
  },
  notes: {
    type: String,
    trim: true
  },
  preferences: {
    preferredBrands: [String],
    preferredCategories: [String],
    communicationMethod: {
      type: String,
      enum: ['phone', 'email', 'whatsapp'],
      default: 'phone'
    }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for better performance
ElectronicsCustomerSchema.index({ userId: 1, phone: 1 }, { unique: true });
ElectronicsCustomerSchema.index({ userId: 1, email: 1 });
ElectronicsCustomerSchema.index({ userId: 1, customerType: 1 });
ElectronicsCustomerSchema.index({ userId: 1, name: 1 });
ElectronicsCustomerSchema.index({ userId: 1, lastVisit: -1 });
ElectronicsCustomerSchema.index({ userId: 1, totalSpent: -1 });

// Virtual for customer loyalty level
ElectronicsCustomerSchema.virtual('loyaltyLevel').get(function() {
  if (this.totalPurchases >= 20 || this.totalSpent >= 50000) return 'vip';
  if (this.totalPurchases >= 5 || this.totalSpent >= 10000) return 'regular';
  return 'new';
});

// Virtual for days since last visit
ElectronicsCustomerSchema.virtual('daysSinceLastVisit').get(function() {
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - this.lastVisit.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual for average purchase value
ElectronicsCustomerSchema.virtual('averagePurchaseValue').get(function() {
  return this.totalPurchases > 0 ? this.totalSpent / this.totalPurchases : 0;
});

// Pre-save middleware to update customer type based on spending
ElectronicsCustomerSchema.pre('save', function(next) {
  // Auto-update customer type based on loyalty level
  this.customerType = this.loyaltyLevel as 'new' | 'regular' | 'vip';
  next();
});

// Method to add purchase
ElectronicsCustomerSchema.methods.addPurchase = function(amount: number) {
  this.totalPurchases += 1;
  this.totalSpent += amount;
  this.lastVisit = new Date();
  return this.save();
};

// Method to add credit
ElectronicsCustomerSchema.methods.addCredit = function(amount: number) {
  this.outstandingCredit += amount;
  return this.save();
};

// Method to pay credit
ElectronicsCustomerSchema.methods.payCredit = function(amount: number) {
  this.outstandingCredit = Math.max(0, this.outstandingCredit - amount);
  return this.save();
};

export default mongoose.models.ElectronicsCustomer || mongoose.model<IElectronicsCustomer>('ElectronicsCustomer', ElectronicsCustomerSchema);
