/**
 * Security utilities for route protection and business model validation
 */

export interface UserPreferences {
  businessModel: 'general' | 'electronics' | null;
  preferredLanguage: 'fr' | 'ar';
  onboardingCompleted: boolean;
  lastLogin: Date;
}

export interface SecurityValidationResult {
  isValid: boolean;
  redirectUrl?: string;
  error?: string;
}

/**
 * Validates if user can access a specific route based on their business model
 */
export function validateRouteAccess(
  userBusinessModel: 'general' | 'electronics' | null,
  requestedPath: string,
  requiredBusinessModel?: 'general' | 'electronics'
): SecurityValidationResult {
  
  // If no business model is required, allow access
  if (!requiredBusinessModel) {
    return { isValid: true };
  }

  // If user hasn't chosen a business model, redirect to landing page
  if (!userBusinessModel) {
    return {
      isValid: false,
      redirectUrl: '/',
      error: 'Business model not selected'
    };
  }

  // If user's business model doesn't match required model, redirect to correct dashboard
  if (userBusinessModel !== requiredBusinessModel) {
    const correctUrl = userBusinessModel === 'electronics' ? '/electronics' : '/dashboard';
    return {
      isValid: false,
      redirectUrl: correctUrl,
      error: `Access denied. User has ${userBusinessModel} model but route requires ${requiredBusinessModel}`
    };
  }

  return { isValid: true };
}

/**
 * Gets the correct dashboard URL based on business model
 */
export function getDashboardUrl(businessModel: 'general' | 'electronics' | null): string {
  if (!businessModel) {
    return '/'; // Redirect to landing page to choose business model
  }
  
  return businessModel === 'electronics' ? '/electronics' : '/dashboard';
}

/**
 * Validates JWT token format (basic client-side validation)
 */
export function isValidTokenFormat(token: string): boolean {
  if (!token) return false;
  
  // JWT should have 3 parts separated by dots
  const parts = token.split('.');
  if (parts.length !== 3) return false;
  
  // Each part should be base64 encoded
  try {
    parts.forEach(part => {
      if (!part) throw new Error('Empty part');
      // Basic base64 validation
      atob(part.replace(/-/g, '+').replace(/_/g, '/'));
    });
    return true;
  } catch {
    return false;
  }
}

/**
 * Sanitizes redirect URLs to prevent open redirect attacks
 */
export function sanitizeRedirectUrl(url: string, allowedDomains: string[] = []): string {
  try {
    const parsedUrl = new URL(url, window.location.origin);
    
    // Only allow same origin or explicitly allowed domains
    if (parsedUrl.origin !== window.location.origin && 
        !allowedDomains.includes(parsedUrl.hostname)) {
      return '/dashboard'; // Default safe redirect
    }
    
    // Only allow specific paths
    const allowedPaths = [
      '/',
      '/dashboard',
      '/electronics',
      '/login',
      '/register'
    ];
    
    const isAllowedPath = allowedPaths.some(path => 
      parsedUrl.pathname === path || parsedUrl.pathname.startsWith(path + '/')
    );
    
    if (!isAllowedPath) {
      return '/dashboard'; // Default safe redirect
    }
    
    return parsedUrl.pathname + parsedUrl.search;
  } catch {
    return '/dashboard'; // Default safe redirect on any error
  }
}

/**
 * Rate limiting helper (client-side)
 */
export class ClientRateLimit {
  private attempts: Map<string, number[]> = new Map();
  private readonly maxAttempts: number;
  private readonly windowMs: number;

  constructor(maxAttempts: number = 5, windowMs: number = 60000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isAllowed(key: string): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < this.windowMs);
    
    if (validAttempts.length >= this.maxAttempts) {
      return false;
    }
    
    // Add current attempt
    validAttempts.push(now);
    this.attempts.set(key, validAttempts);
    
    return true;
  }

  getRemainingTime(key: string): number {
    const attempts = this.attempts.get(key) || [];
    if (attempts.length < this.maxAttempts) return 0;
    
    const oldestAttempt = Math.min(...attempts);
    const remainingTime = this.windowMs - (Date.now() - oldestAttempt);
    
    return Math.max(0, remainingTime);
  }
}

/**
 * Security constants
 */
export const SECURITY_CONFIG = {
  JWT_EXPIRY: '24h',
  MAX_LOGIN_ATTEMPTS: 5,
  LOGIN_ATTEMPT_WINDOW: 15 * 60 * 1000, // 15 minutes
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  ALLOWED_BUSINESS_MODELS: ['general', 'electronics'] as const,
  PROTECTED_ROUTES: [
    '/dashboard',
    '/electronics',
    '/products',
    '/customers',
    '/sales',
    '/purchases',
    '/returns',
    '/credit',
    '/analytics'
  ],
  PUBLIC_ROUTES: [
    '/',
    '/login',
    '/register'
  ]
} as const;
