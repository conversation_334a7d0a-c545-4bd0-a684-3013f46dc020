'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Save,
  ArrowLeft,
  RotateCcw,
  Search,
  User,
  Package,
  FileText,
  AlertTriangle,
  DollarSign,
  Calendar
} from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  date: string;
  total: number;
  products: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    serialNumber?: string;
  }>;
}

interface ReturnData {
  returnNumber: string;
  customerId: string;
  invoiceId: string;
  returnDate: string;
  returnType: 'refund' | 'exchange' | 'store_credit';
  reason: string;
  condition: 'new' | 'used' | 'damaged' | 'defective';
  items: Array<{
    productId: string;
    productName: string;
    quantity: number;
    originalPrice: number;
    returnQuantity: number;
    refundAmount: number;
    serialNumber?: string;
  }>;
  totalRefundAmount: number;
  restockingFee: number;
  notes: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  processedBy: string;
}

export default function NewReturnPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [customerInvoices, setCustomerInvoices] = useState<Invoice[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [customerSearch, setCustomerSearch] = useState('');
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  
  const [returnData, setReturnData] = useState<ReturnData>({
    returnNumber: `RET-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
    customerId: '',
    invoiceId: '',
    returnDate: new Date().toISOString().split('T')[0],
    returnType: 'refund',
    reason: '',
    condition: 'new',
    items: [],
    totalRefundAmount: 0,
    restockingFee: 0,
    notes: '',
    status: 'pending',
    processedBy: 'Current User' // Replace with actual user
  });

  useEffect(() => {
    // Mock customers data - replace with actual API call
    const mockCustomers: Customer[] = [
      {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890'
      },
      {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+1234567891'
      },
      {
        id: '3',
        name: 'Bob Johnson',
        email: '<EMAIL>',
        phone: '+1234567892'
      }
    ];
    setCustomers(mockCustomers);
  }, []);

  useEffect(() => {
    if (selectedCustomer) {
      // Mock invoices for selected customer
      const mockInvoices: Invoice[] = [
        {
          id: '1',
          invoiceNumber: 'INV-2024-001',
          date: '2024-01-15',
          total: 1130.8,
          products: [
            {
              id: '1',
              name: 'iPhone 15 Pro',
              quantity: 1,
              price: 1130.8,
              serialNumber: 'IPH123456789'
            }
          ]
        },
        {
          id: '2',
          invoiceNumber: 'INV-2024-002',
          date: '2024-01-20',
          total: 899.99,
          products: [
            {
              id: '2',
              name: 'Samsung Galaxy S24',
              quantity: 1,
              price: 899.99,
              serialNumber: 'SGS987654321'
            }
          ]
        }
      ];
      
      setCustomerInvoices(mockInvoices);
    } else {
      setCustomerInvoices([]);
    }
  }, [selectedCustomer]);

  useEffect(() => {
    // Calculate total refund amount
    const total = returnData.items.reduce((sum, item) => sum + item.refundAmount, 0);
    const finalAmount = total - returnData.restockingFee;
    setReturnData(prev => ({ ...prev, totalRefundAmount: Math.max(0, finalAmount) }));
  }, [returnData.items, returnData.restockingFee]);

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
    customer.email.toLowerCase().includes(customerSearch.toLowerCase())
  );

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
    setCustomerSearch(customer.name);
    setShowCustomerDropdown(false);
    setReturnData(prev => ({ ...prev, customerId: customer.id }));
    setSelectedInvoice(null);
  };

  const handleInvoiceSelect = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setReturnData(prev => ({ 
      ...prev, 
      invoiceId: invoice.id,
      items: invoice.products.map(product => ({
        productId: product.id,
        productName: product.name,
        quantity: product.quantity,
        originalPrice: product.price,
        returnQuantity: 0,
        refundAmount: 0,
        serialNumber: product.serialNumber
      }))
    }));
  };

  const handleItemReturnQuantityChange = (productId: string, returnQuantity: number) => {
    setReturnData(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.productId === productId) {
          const maxQuantity = Math.min(returnQuantity, item.quantity);
          const refundAmount = (item.originalPrice / item.quantity) * maxQuantity;
          return {
            ...item,
            returnQuantity: maxQuantity,
            refundAmount: refundAmount
          };
        }
        return item;
      })
    }));
  };

  const handleSave = async () => {
    // Validation
    if (!selectedCustomer) {
      alert('Please select a customer');
      return;
    }
    
    if (!selectedInvoice) {
      alert('Please select an invoice');
      return;
    }
    
    if (!returnData.reason.trim()) {
      alert('Please provide a reason for the return');
      return;
    }

    const hasReturnItems = returnData.items.some(item => item.returnQuantity > 0);
    if (!hasReturnItems) {
      alert('Please select at least one item to return');
      return;
    }

    setLoading(true);
    
    try {
      // Mock API call - replace with actual API
      const returnPayload = {
        ...returnData,
        customer: selectedCustomer,
        invoice: selectedInvoice,
        createdAt: new Date().toISOString()
      };

      console.log('Saving return:', returnPayload);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Redirect to returns list
      router.push('/electronics/returns');
    } catch (error) {
      console.error('Error saving return:', error);
      alert('Error saving return. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const returnReasons = [
    'Defective product',
    'Wrong item received',
    'Changed mind',
    'Product not as described',
    'Damaged during shipping',
    'Quality issues',
    'Compatibility issues',
    'Other'
  ];

  const returnConditions = [
    { value: 'new', label: 'New/Unopened' },
    { value: 'used', label: 'Used/Good condition' },
    { value: 'damaged', label: 'Damaged' },
    { value: 'defective', label: 'Defective' }
  ];

  const returnTypes = [
    { value: 'refund', label: 'Refund' },
    { value: 'exchange', label: 'Exchange' },
    { value: 'store_credit', label: 'Store Credit' }
  ];

  const canReturn = (invoiceDate: string) => {
    const daysSincePurchase = Math.floor((new Date().getTime() - new Date(invoiceDate).getTime()) / (1000 * 60 * 60 * 24));
    return daysSincePurchase <= 30; // 30-day return policy
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Process New Return</h1>
            <p className="text-gray-600">Process a product return or exchange</p>
          </div>
        </div>
        <button
          onClick={handleSave}
          disabled={loading}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-blue-700 disabled:opacity-50"
        >
          <Save className="h-5 w-5 mr-2" />
          {loading ? 'Processing...' : 'Process Return'}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Return Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Return Details */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <RotateCcw className="h-5 w-5 text-blue-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Return Details</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Return Number
                </label>
                <input
                  type="text"
                  value={returnData.returnNumber}
                  onChange={(e) => setReturnData({...returnData, returnNumber: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Return Date
                </label>
                <input
                  type="date"
                  value={returnData.returnDate}
                  onChange={(e) => setReturnData({...returnData, returnDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Return Type
                </label>
                <select
                  value={returnData.returnType}
                  onChange={(e) => setReturnData({...returnData, returnType: e.target.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {returnTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product Condition
                </label>
                <select
                  value={returnData.condition}
                  onChange={(e) => setReturnData({...returnData, condition: e.target.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {returnConditions.map(condition => (
                    <option key={condition.value} value={condition.value}>{condition.label}</option>
                  ))}
                </select>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reason for Return *
                </label>
                <select
                  value={returnData.reason}
                  onChange={(e) => setReturnData({...returnData, reason: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select reason</option>
                  {returnReasons.map(reason => (
                    <option key={reason} value={reason}>{reason}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Customer Selection */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <User className="h-5 w-5 text-green-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Customer Information</h3>
            </div>
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Customer *
              </label>
              <div className="relative">
                <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search customers..."
                  value={customerSearch}
                  onChange={(e) => {
                    setCustomerSearch(e.target.value);
                    setShowCustomerDropdown(true);
                  }}
                  onFocus={() => setShowCustomerDropdown(true)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              {showCustomerDropdown && filteredCustomers.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {filteredCustomers.map((customer) => (
                    <button
                      key={customer.id}
                      onClick={() => handleCustomerSelect(customer)}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
                    >
                      <div className="font-medium text-gray-900">{customer.name}</div>
                      <div className="text-sm text-gray-500">{customer.email}</div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Invoice Selection */}
          {selectedCustomer && customerInvoices.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center mb-4">
                <FileText className="h-5 w-5 text-orange-500 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Select Purchase Invoice</h3>
              </div>
              <div className="space-y-2">
                {customerInvoices.map((invoice) => (
                  <div
                    key={invoice.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedInvoice?.id === invoice.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleInvoiceSelect(invoice)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium text-gray-900">{invoice.invoiceNumber}</div>
                        <div className="text-sm text-gray-500">
                          Date: {new Date(invoice.date).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-gray-900">${invoice.total.toFixed(2)}</div>
                        <div className={`text-xs ${
                          canReturn(invoice.date) ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {canReturn(invoice.date) ? 'Returnable' : 'Return period expired'}
                        </div>
                      </div>
                    </div>
                    {!canReturn(invoice.date) && (
                      <div className="mt-2 flex items-center text-red-600">
                        <AlertTriangle className="h-4 w-4 mr-1" />
                        <span className="text-xs">This invoice is outside the 30-day return window</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Return Items */}
          {selectedInvoice && (
            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center mb-4">
                <Package className="h-5 w-5 text-purple-500 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Items to Return</h3>
              </div>
              <div className="space-y-4">
                {returnData.items.map((item) => (
                  <div key={item.productId} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium text-gray-900">{item.productName}</h4>
                        <p className="text-sm text-gray-500">
                          Original quantity: {item.quantity} • Price: ${item.originalPrice.toFixed(2)}
                        </p>
                        {item.serialNumber && (
                          <p className="text-xs text-gray-500">Serial: {item.serialNumber}</p>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Return Quantity
                        </label>
                        <input
                          type="number"
                          min="0"
                          max={item.quantity}
                          value={item.returnQuantity}
                          onChange={(e) => handleItemReturnQuantityChange(item.productId, parseInt(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Unit Refund
                        </label>
                        <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg">
                          ${(item.originalPrice / item.quantity).toFixed(2)}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total Refund
                        </label>
                        <div className="px-3 py-2 bg-green-50 border border-green-300 rounded-lg font-bold text-green-600">
                          ${item.refundAmount.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Fees and Notes */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Restocking Fee
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={returnData.restockingFee}
                  onChange={(e) => setReturnData({...returnData, restockingFee: parseFloat(e.target.value) || 0})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={returnData.status}
                  onChange={(e) => setReturnData({...returnData, status: e.target.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Additional Notes
              </label>
              <textarea
                rows={3}
                value={returnData.notes}
                onChange={(e) => setReturnData({...returnData, notes: e.target.value})}
                placeholder="Any additional notes about the return..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Return Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow sticky top-6">
            <div className="flex items-center mb-4">
              <RotateCcw className="h-5 w-5 text-blue-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Return Summary</h3>
            </div>
            
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Return Number</div>
                <div className="font-medium text-gray-900">{returnData.returnNumber}</div>
              </div>
              
              {selectedCustomer && (
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Customer</div>
                  <div className="font-medium text-gray-900">{selectedCustomer.name}</div>
                  <div className="text-sm text-gray-600">{selectedCustomer.email}</div>
                </div>
              )}
              
              {selectedInvoice && (
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Original Invoice</div>
                  <div className="font-medium text-orange-600">{selectedInvoice.invoiceNumber}</div>
                  <div className="text-sm text-gray-600">
                    {new Date(selectedInvoice.date).toLocaleDateString()}
                  </div>
                </div>
              )}
              
              <div className="p-4 bg-purple-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Return Type</div>
                <div className="font-medium text-purple-600 capitalize">
                  {returnData.returnType.replace('_', ' ')}
                </div>
              </div>
              
              {returnData.items.length > 0 && (
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Items to Return</div>
                  <div className="space-y-1">
                    {returnData.items.filter(item => item.returnQuantity > 0).map(item => (
                      <div key={item.productId} className="text-sm">
                        <span className="font-medium">{item.productName}</span>
                        <span className="text-gray-600"> × {item.returnQuantity}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Refund Calculation</div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>${(returnData.totalRefundAmount + returnData.restockingFee).toFixed(2)}</span>
                  </div>
                  {returnData.restockingFee > 0 && (
                    <div className="flex justify-between text-red-600">
                      <span>Restocking Fee:</span>
                      <span>-${returnData.restockingFee.toFixed(2)}</span>
                    </div>
                  )}
                  <hr className="my-2" />
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total Refund:</span>
                    <span className="text-green-600">${returnData.totalRefundAmount.toFixed(2)}</span>
                  </div>
                </div>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Status</div>
                <div className={`font-medium capitalize ${
                  returnData.status === 'approved' || returnData.status === 'completed' ? 'text-green-600' :
                  returnData.status === 'rejected' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  {returnData.status}
                </div>
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={handleSave}
                disabled={loading || !selectedCustomer || !selectedInvoice || !returnData.reason.trim()}
                className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Processing...' : 'Process Return'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
