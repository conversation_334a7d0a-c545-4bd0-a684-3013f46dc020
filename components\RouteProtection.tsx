'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';

interface RouteProtectionProps {
  children: React.ReactNode;
  requiredBusinessModel?: 'general' | 'electronics';
  requireAuth?: boolean;
}

export default function RouteProtection({
  children,
  requiredBusinessModel,
  requireAuth = true
}: RouteProtectionProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkAuthorization();
  }, [pathname]);

  const checkAuthorization = async () => {
    try {
      const token = localStorage.getItem('auth-token');

      // Check authentication requirement
      if (requireAuth && !token) {
        router.push(`/login?redirect=${encodeURIComponent(pathname)}`);
        return;
      }

      // If no auth required and no token, allow access
      if (!requireAuth && !token) {
        setIsAuthorized(true);
        setIsLoading(false);
        return;
      }

      // If we have a token, verify it and check business model access
      if (token) {
        const response = await fetch('/api/user/preferences', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          // Invalid token or server error
          localStorage.removeItem('auth-token');
          localStorage.removeItem('user');
          router.push('/login');
          return;
        }

        const data = await response.json();
        const userBusinessModel = data.preferences?.businessModel;
        console.log(`RouteProtection - User business model: ${userBusinessModel}, Required: ${requiredBusinessModel}`);

        // Check business model requirement
        if (requiredBusinessModel) {
          if (!userBusinessModel) {
            // User hasn't chosen a business model, redirect to landing page
            console.log('RouteProtection - No business model, redirecting to landing page');
            router.push('/');
            return;
          }

          if (userBusinessModel !== requiredBusinessModel) {
            // User has wrong business model, redirect to their correct dashboard
            const correctUrl = userBusinessModel === 'electronics' ? '/electronics' : '/dashboard';
            console.log(`RouteProtection - Wrong business model, redirecting to: ${correctUrl}`);
            router.push(correctUrl);
            return;
          }
        }

        // All checks passed
        setIsAuthorized(true);
      }
    } catch (error) {
      console.error('Authorization check failed:', error);
      if (requireAuth) {
        router.push('/login');
      } else {
        setIsAuthorized(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 font-medium">التحقق من الصلاحيات...</p>
          <p className="text-sm text-gray-500">Vérification des autorisations...</p>
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return null; // Will redirect, so don't render anything
  }

  return <>{children}</>;
}
