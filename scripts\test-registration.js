// Test script to verify phone/email registration works
// This script tests the registration logic without running the full Next.js server

const mongoose = require('mongoose');

// Mock the User model structure
const UserSchema = new mongoose.Schema({
  email: {
    type: String,
    lowercase: true,
    trim: true,
    match: [/^\S+@\S+\.\S+$/, 'Please enter a valid email address'],
    default: null,
  },
  phone: {
    type: String,
    trim: true,
    match: [/^(\+212|0)[5-7][0-9]{8}$/, 'Please enter a valid Moroccan phone number'],
    default: null,
  },
  hashedPassword: {
    type: String,
    required: [true, 'Password is required'],
    minlength: 6,
  },
  preferredLanguage: {
    type: String,
    enum: ['fr', 'ar'],
    default: 'fr',
  },
}, {
  timestamps: true,
});

// Ensure at least one of email or phone is provided
UserSchema.pre('validate', function (next) {
  if (!this.email && !this.phone) {
    next(new Error('Either email or phone number is required'));
  } else {
    next();
  }
});

// Create sparse unique indexes
UserSchema.index({ email: 1 }, { unique: true, sparse: true });
UserSchema.index({ phone: 1 }, { unique: true, sparse: true });

// Phone validation functions
function isValidPhone(phone) {
  const phoneRegex = /^(\+212|0)[5-7][0-9]{8}$/;
  return phoneRegex.test(phone);
}

function normalizePhone(phone) {
  const cleaned = phone.replace(/[\s-]/g, '');
  
  if (cleaned.startsWith('0') && cleaned.length === 10) {
    return '+212' + cleaned.substring(1);
  }
  
  if (cleaned.startsWith('+212')) {
    return cleaned;
  }
  
  return cleaned;
}

async function testRegistration() {
  try {
    // Connect to MongoDB
    const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crm-hanout-test';
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Clear existing model if it exists
    if (mongoose.models.TestUser) {
      delete mongoose.models.TestUser;
    }

    const TestUser = mongoose.model('TestUser', UserSchema);

    // Clear test collection
    await TestUser.deleteMany({});
    console.log('🧹 Cleared test collection');

    // Test cases
    const testCases = [
      {
        name: 'Email only registration',
        data: { email: '<EMAIL>', phone: null, hashedPassword: 'hashedpass123', preferredLanguage: 'fr' }
      },
      {
        name: 'Phone only registration',
        data: { email: null, phone: '+212612345678', hashedPassword: 'hashedpass123', preferredLanguage: 'ar' }
      },
      {
        name: 'Both email and phone',
        data: { email: '<EMAIL>', phone: '+212687654321', hashedPassword: 'hashedpass123', preferredLanguage: 'fr' }
      },
      {
        name: 'Duplicate email (should fail)',
        data: { email: '<EMAIL>', phone: null, hashedPassword: 'hashedpass123', preferredLanguage: 'fr' }
      },
      {
        name: 'Duplicate phone (should fail)',
        data: { email: null, phone: '+212612345678', hashedPassword: 'hashedpass123', preferredLanguage: 'ar' }
      },
      {
        name: 'Neither email nor phone (should fail)',
        data: { email: null, phone: null, hashedPassword: 'hashedpass123', preferredLanguage: 'fr' }
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n🧪 Testing: ${testCase.name}`);
      try {
        const user = new TestUser(testCase.data);
        await user.save();
        console.log(`✅ Success: User created with ID ${user._id}`);
      } catch (error) {
        if (error.code === 11000) {
          console.log('❌ Expected failure: Duplicate key error');
        } else if (error.message.includes('Either email or phone number is required')) {
          console.log('❌ Expected failure: Missing email and phone');
        } else {
          console.log(`❌ Unexpected error: ${error.message}`);
        }
      }
    }

    // Test phone normalization
    console.log('\n📱 Testing phone normalization:');
    const phoneTests = [
      '0612345678',
      '06 12 34 56 78',
      '06-12-34-56-78',
      '+212612345678'
    ];

    for (const phone of phoneTests) {
      const normalized = normalizePhone(phone);
      const isValid = isValidPhone(normalized);
      console.log(`  ${phone} → ${normalized} (${isValid ? 'valid' : 'invalid'})`);
    }

    console.log('\n🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testRegistration().catch(console.error);
}

module.exports = { testRegistration };
