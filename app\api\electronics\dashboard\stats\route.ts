import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import ElectronicsProduct from '@/models/ElectronicsProduct';
import ElectronicsCustomer from '@/models/ElectronicsCustomer';
import ElectronicsInvoice from '@/models/ElectronicsInvoice';
import { startOfDay, startOfMonth, endOfMonth } from 'date-fns';

export async function GET(request: NextRequest) {
  try {
    console.log('Electronics dashboard stats request received');
    
    // Check authentication
    const user = getUserFromRequest(request);
    if (!user) {
      console.log('Unauthorized electronics dashboard access attempt');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const userId = user.id;
    const today = startOfDay(new Date());
    const monthStart = startOfMonth(new Date());
    const monthEnd = endOfMonth(new Date());

    // Inventory Stats
    const [totalProducts, lowStockProducts, inventoryValue] = await Promise.all([
      ElectronicsProduct.countDocuments({ userId, isActive: true }),
      ElectronicsProduct.countDocuments({ 
        userId, 
        isActive: true,
        $expr: { $lte: ['$quantity', '$minStockLevel'] }
      }),
      ElectronicsProduct.aggregate([
        { $match: { userId, isActive: true } },
        { 
          $group: { 
            _id: null, 
            totalValue: { $sum: { $multiply: ['$quantity', '$costPrice'] } },
            categories: { $addToSet: '$category' }
          } 
        }
      ])
    ]);

    const inventoryStats = {
      totalProducts,
      lowStockItems: lowStockProducts,
      totalValue: inventoryValue[0]?.totalValue || 0,
      categories: inventoryValue[0]?.categories?.length || 0
    };

    // Customer Stats
    const [totalCustomers, newCustomersThisMonth, vipCustomers, totalCredit] = await Promise.all([
      ElectronicsCustomer.countDocuments({ userId, isActive: true }),
      ElectronicsCustomer.countDocuments({ 
        userId, 
        isActive: true,
        createdAt: { $gte: monthStart, $lte: monthEnd }
      }),
      ElectronicsCustomer.countDocuments({ userId, isActive: true, customerType: 'vip' }),
      ElectronicsCustomer.aggregate([
        { $match: { userId, isActive: true } },
        { $group: { _id: null, totalCredit: { $sum: '$outstandingCredit' } } }
      ])
    ]);

    const customerStats = {
      totalCustomers,
      newThisMonth: newCustomersThisMonth,
      vipCustomers,
      totalCredit: totalCredit[0]?.totalCredit || 0
    };

    // Sales Stats
    const [todayRevenue, monthlyRevenue, totalInvoices, unpaidInvoices] = await Promise.all([
      ElectronicsInvoice.aggregate([
        { 
          $match: { 
            userId, 
            isActive: true,
            createdAt: { $gte: today }
          } 
        },
        { $group: { _id: null, revenue: { $sum: '$totalAmount' } } }
      ]),
      ElectronicsInvoice.aggregate([
        { 
          $match: { 
            userId, 
            isActive: true,
            createdAt: { $gte: monthStart, $lte: monthEnd }
          } 
        },
        { $group: { _id: null, revenue: { $sum: '$totalAmount' } } }
      ]),
      ElectronicsInvoice.countDocuments({ userId, isActive: true }),
      ElectronicsInvoice.countDocuments({ 
        userId, 
        isActive: true, 
        paymentStatus: { $in: ['unpaid', 'partial'] }
      })
    ]);

    const salesStats = {
      todayRevenue: todayRevenue[0]?.revenue || 0,
      monthlyRevenue: monthlyRevenue[0]?.revenue || 0,
      totalInvoices,
      unpaidInvoices
    };

    // Warranty Stats
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
    
    const [activeWarranties, expiringThisMonth, expiredWarranties] = await Promise.all([
      ElectronicsInvoice.aggregate([
        { $match: { userId, isActive: true } },
        { $unwind: '$items' },
        { $match: { 'items.warrantyExpiry': { $gt: now } } },
        { $count: 'total' }
      ]),
      ElectronicsInvoice.aggregate([
        { $match: { userId, isActive: true } },
        { $unwind: '$items' },
        { 
          $match: { 
            'items.warrantyExpiry': { 
              $gt: now,
              $lte: nextMonth
            }
          } 
        },
        { $count: 'total' }
      ]),
      ElectronicsInvoice.aggregate([
        { $match: { userId, isActive: true } },
        { $unwind: '$items' },
        { $match: { 'items.warrantyExpiry': { $lte: now } } },
        { $count: 'total' }
      ])
    ]);

    const warrantyStats = {
      activeWarranties: activeWarranties[0]?.total || 0,
      expiringThisMonth: expiringThisMonth[0]?.total || 0,
      expiredWarranties: expiredWarranties[0]?.total || 0
    };

    console.log('Electronics dashboard stats compiled successfully');

    return NextResponse.json({
      inventory: inventoryStats,
      customers: customerStats,
      sales: salesStats,
      warranty: warrantyStats
    });

  } catch (error) {
    console.error('Electronics dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    );
  }
}
