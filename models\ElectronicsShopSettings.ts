import mongoose, { Document, Schema } from 'mongoose';

export interface IElectronicsShopSettings extends Document {
  userId: mongoose.Types.ObjectId;
  shopName: string;
  ownerName: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  contact: {
    phone: string;
    email?: string;
    whatsapp?: string;
    website?: string;
  };
  business: {
    taxId?: string;
    licenseNumber?: string;
    registrationNumber?: string;
  };
  branding: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
  invoiceSettings: {
    defaultTaxRate: number;
    invoicePrefix: string;
    termsAndConditions?: string;
    footerText?: string;
  };
  notifications: {
    lowStockAlert: boolean;
    warrantyExpiryAlert: boolean;
    paymentReminder: boolean;
    emailNotifications: boolean;
    whatsappNotifications: boolean;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ElectronicsShopSettingsSchema = new Schema<IElectronicsShopSettings>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
    index: true
  },
  shopName: {
    type: String,
    required: true,
    trim: true
  },
  ownerName: {
    type: String,
    required: true,
    trim: true
  },
  address: {
    street: {
      type: String,
      required: true,
      trim: true
    },
    city: {
      type: String,
      required: true,
      trim: true
    },
    state: {
      type: String,
      required: true,
      trim: true
    },
    zipCode: {
      type: String,
      required: true,
      trim: true
    },
    country: {
      type: String,
      required: true,
      trim: true,
      default: 'Morocco'
    }
  },
  contact: {
    phone: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      trim: true,
      lowercase: true
    },
    whatsapp: {
      type: String,
      trim: true
    },
    website: {
      type: String,
      trim: true
    }
  },
  business: {
    taxId: {
      type: String,
      trim: true
    },
    licenseNumber: {
      type: String,
      trim: true
    },
    registrationNumber: {
      type: String,
      trim: true
    }
  },
  branding: {
    logo: String,
    primaryColor: {
      type: String,
      default: '#3B82F6'
    },
    secondaryColor: {
      type: String,
      default: '#1F2937'
    }
  },
  invoiceSettings: {
    defaultTaxRate: {
      type: Number,
      default: 20,
      min: 0,
      max: 100
    },
    invoicePrefix: {
      type: String,
      default: 'INV',
      trim: true,
      uppercase: true
    },
    termsAndConditions: {
      type: String,
      trim: true,
      default: 'All sales are final. Warranty terms apply as per manufacturer guidelines.'
    },
    footerText: {
      type: String,
      trim: true,
      default: 'Thank you for your business!'
    }
  },
  notifications: {
    lowStockAlert: {
      type: Boolean,
      default: true
    },
    warrantyExpiryAlert: {
      type: Boolean,
      default: true
    },
    paymentReminder: {
      type: Boolean,
      default: true
    },
    emailNotifications: {
      type: Boolean,
      default: false
    },
    whatsappNotifications: {
      type: Boolean,
      default: true
    }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for user lookup
ElectronicsShopSettingsSchema.index({ userId: 1 });

export default mongoose.models.ElectronicsShopSettings || mongoose.model<IElectronicsShopSettings>('ElectronicsShopSettings', ElectronicsShopSettingsSchema);
