'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import {
  Store,
  Users,
  CreditCard,
  BarChart3,
  ArrowRight,
  Menu,
  X,
  Smartphone,
  CheckCircle,
  Globe,
  Star,
  User,
  Phone,
  Mail
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import DashboardRedirect from '@/components/DashboardRedirect';

export default function HomePage() {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: ''
  });

  const isRTL = locale === 'ar';

  const switchLanguage = (newLocale: string) => {
    const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);
    router.push(newPath);
  };

  const handleGetStarted = () => {
    router.push(`/${locale}/register`);
  };

  const handleLogin = () => {
    router.push(`/${locale}/login`);
  };

  const handleMoulHanoutClick = () => {
    router.push(`/${locale}/moul-hanout`);
  };

  const handleElectronicsClick = () => {
    router.push(`/${locale}/electronic-shop`);
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams({
      name: formData.name,
      phone: formData.phone,
      email: formData.email
    });
    router.push(`/${locale}/register?${params.toString()}`);
  };

  return (
    <DashboardRedirect>
      <div className={`min-h-screen bg-white ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
        {/* Navigation */}
        <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              {/* Logo */}
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-green-600 to-red-600 rounded-lg flex items-center justify-center">
                  <Store className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">{t('homepage.title')}</h1>
                  <p className="text-xs text-gray-500 hidden sm:block">{t('homepage.subtitle')}</p>
                </div>
              </div>

              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center gap-4">
                {/* Language Switcher */}
                <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                  <Button
                    variant={locale === 'fr' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => switchLanguage('fr')}
                    className="text-xs"
                  >
                    FR
                  </Button>
                  <Button
                    variant={locale === 'ar' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => switchLanguage('ar')}
                    className="text-xs"
                  >
                    AR
                  </Button>
                </div>

                <Button variant="ghost" onClick={handleLogin}>
                  {t('nav.login')}
                </Button>
                <Button onClick={handleGetStarted} className="bg-green-600 hover:bg-green-700">
                  {t('nav.tryFree')}
                </Button>
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                >
                  {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                </Button>
              </div>
            </div>

            {/* Mobile Navigation */}
            {isMenuOpen && (
              <div className="md:hidden border-t border-gray-200 py-4 space-y-2">
                {/* Mobile Language Switcher */}
                <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1 w-fit">
                  <Button
                    variant={locale === 'fr' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => switchLanguage('fr')}
                    className="text-xs"
                  >
                    FR
                  </Button>
                  <Button
                    variant={locale === 'ar' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => switchLanguage('ar')}
                    className="text-xs"
                  >
                    AR
                  </Button>
                </div>

                <Button variant="ghost" className="w-full justify-start" onClick={handleLogin}>
                  {t('nav.login')}
                </Button>
                <Button className="w-full bg-green-600 hover:bg-green-700" onClick={handleGetStarted}>
                  {t('nav.tryFree')}
                </Button>
              </div>
            )}
          </div>
        </nav>

        {/* Hero Section */}
        <section className="bg-gradient-to-br from-green-50 via-white to-red-50 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-8">
              {/* Main Headlines */}
              <div className="space-y-4">
                <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
                  <span className="block">{t('homepage.heroTitle')}</span>
                </h1>

                <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                  {t('homepage.heroSubtitle')}
                </p>

                <p className="text-lg text-gray-600 max-w-4xl mx-auto">
                  {t('homepage.heroDescription')}
                </p>
              </div>

              {/* Value Proposition */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 md:p-8 max-w-4xl mx-auto border border-gray-200 shadow-lg">
                <div className="grid md:grid-cols-3 gap-6 text-center">
                  <div className="space-y-2">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                      <Users className="w-6 h-6 text-green-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{t('homepage.features.customerManagement')}</h3>
                    <p className="text-sm text-gray-600">{t('homepage.features.customerManagementDesc')}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                      <CreditCard className="w-6 h-6 text-red-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{t('homepage.features.creditTracking')}</h3>
                    <p className="text-sm text-gray-600">{t('homepage.features.creditTrackingDesc')}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                      <BarChart3 className="w-6 h-6 text-blue-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{t('homepage.features.salesAnalytics')}</h3>
                    <p className="text-sm text-gray-600">{t('homepage.features.salesAnalyticsDesc')}</p>
                  </div>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button
                  size="lg"
                  className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                  onClick={handleGetStarted}
                >
                  {t('homepage.getStarted')}
                  <ArrowRight className={`${isRTL ? 'mr-2' : 'ml-2'} h-5 w-5`} />
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-gray-300 px-8 py-4 text-lg font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200"
                  onClick={handleLogin}
                >
                  {t('nav.login')}
                </Button>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-gray-500 pt-8">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>{t('homepage.benefits.free')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>{t('homepage.benefits.simple')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>{t('homepage.benefits.arabic')}</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Business Model Selection */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {t('homepage.chooseBusinessType')}
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                {t('homepage.chooseDescription')}
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
              {/* General Store CRM */}
              <Card className="border-2 border-gray-200 hover:border-green-500 transition-all duration-300 hover:shadow-xl group cursor-pointer"
                onClick={handleMoulHanoutClick}>
                <CardContent className="p-8 text-center space-y-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-red-500 rounded-3xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                    <Store className="w-10 h-10 text-white" />
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-2xl font-bold text-gray-900">
                      {t('homepage.moulHanoutTitle')}
                    </h3>
                    <p className="text-lg text-gray-600">
                      {t('homepage.moulHanoutSubtitle')}
                    </p>
                    <p className="text-gray-600 leading-relaxed">
                      {t('homepage.moulHanoutDescription')}
                    </p>
                  </div>

                  <Button
                    className="w-full bg-gradient-to-r from-green-600 to-red-600 hover:from-green-700 hover:to-red-700 text-white font-semibold py-3 rounded-xl group-hover:shadow-lg transition-all duration-300"
                  >
                    {t('homepage.moulHanoutCta')}
                    <ArrowRight className={`${isRTL ? 'mr-2' : 'ml-2'} h-4 w-4`} />
                  </Button>
                </CardContent>
              </Card>

              {/* Electronics Store CRM */}
              <Card className="border-2 border-gray-200 hover:border-blue-500 transition-all duration-300 hover:shadow-xl group cursor-pointer"
                onClick={handleElectronicsClick}>
                <CardContent className="p-8 text-center space-y-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                    <Smartphone className="w-10 h-10 text-white" />
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-2xl font-bold text-gray-900">
                      {t('homepage.electronicsTitle')}
                    </h3>
                    <p className="text-lg text-gray-600">
                      {t('homepage.electronicsSubtitle')}
                    </p>
                    <p className="text-gray-600 leading-relaxed">
                      {t('homepage.electronicsDescription')}
                    </p>
                  </div>

                  <Button
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 rounded-xl group-hover:shadow-lg transition-all duration-300"
                  >
                    {t('homepage.electronicsCta')}
                    <ArrowRight className={`${isRTL ? 'mr-2' : 'ml-2'} h-4 w-4`} />
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-white py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-6">
              <div className="flex items-center justify-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-red-600 rounded-lg flex items-center justify-center">
                  <Store className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold">{t('homepage.title')}</h3>
                  <p className="text-sm text-gray-400">{t('homepage.subtitle')}</p>
                </div>
              </div>

              <p className="text-gray-400 max-w-2xl mx-auto">
                {t('homepage.heroSubtitle')}
              </p>

              <div className="border-t border-gray-800 pt-6">
                <p className="text-sm text-gray-500">
                  © 2024 {t('homepage.title')}. {locale === 'ar' ? 'جميع الحقوق محفوظة' : 'Tous droits réservés'}
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </DashboardRedirect>
  );
}
