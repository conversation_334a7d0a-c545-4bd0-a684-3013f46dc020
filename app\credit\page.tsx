'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Navigation from '@/components/Navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { 
  CreditCard, 
  DollarSign,
  User,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Eye,
  History
} from 'lucide-react';
import { getMessages, type Locale, getLocaleFromString } from '@/lib/i18n';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { format } from 'date-fns';
import { fr, ar } from 'date-fns/locale';

interface Customer {
  _id: string;
  name: string;
  phone?: string;
}

interface CreditSale {
  _id: string;
  productId: {
    _id: string;
    name: string;
    unit: string;
  };
  customerId: {
    _id: string;
    name: string;
    phone?: string;
  };
  quantity: number;
  sellPrice: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  saleDate: string;
  notes?: string;
}

interface CustomerSummary {
  _id: string;
  customer: Customer;
  totalDebt: number;
  salesCount: number;
  oldestSale: string;
}

export default function CreditPage() {
  const [locale, setLocale] = useState<Locale>('fr');
  const [creditSales, setCreditSales] = useState<CreditSale[]>([]);
  const [customerSummary, setCustomerSummary] = useState<CustomerSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState('all');
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [selectedSale, setSelectedSale] = useState<CreditSale | null>(null);
  const [paymentData, setPaymentData] = useState({
    paymentAmount: 0,
    notes: ''
  });
  const [submitting, setSubmitting] = useState(false);
  const router = useRouter();
  const messages = getMessages(locale);

  console.log('Credit page rendered');

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem('auth-token');
    const userData = localStorage.getItem('user');
    
    if (!token || !userData) {
      console.log('No authentication found, redirecting to login');
      router.push('/login');
      return;
    }

    // Set user's preferred language
    const user = JSON.parse(userData);
    setLocale(getLocaleFromString(user.preferredLanguage));
    
    fetchCreditData();
  }, [router, selectedCustomer]);

  const fetchCreditData = async () => {
    console.log('Fetching credit data...');
    try {
      const token = localStorage.getItem('auth-token');
      const params = new URLSearchParams();
      
      if (selectedCustomer && selectedCustomer !== 'all') params.append('customerId', selectedCustomer);
      
      const response = await fetch(`/api/credit?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.log('Token expired, redirecting to login');
          localStorage.removeItem('auth-token');
          localStorage.removeItem('user');
          router.push('/login');
          return;
        }
        throw new Error('Failed to fetch credit data');
      }

      const data = await response.json();
      console.log('Credit data fetched:', {
        creditSales: data.creditSales.length,
        customerSummary: data.customerSummary.length
      });

      setCreditSales(data.creditSales);
      setCustomerSummary(data.customerSummary);
    } catch (err) {
      console.error('Credit data fetch error:', err);
      setError('Failed to load credit data');
    } finally {
      setLoading(false);
    }
  };

  const handleLocaleChange = (newLocale: Locale) => {
    console.log('Language changed to:', newLocale);
    setLocale(newLocale);
    
    const userData = localStorage.getItem('user');
    if (userData) {
      const user = JSON.parse(userData);
      user.preferredLanguage = newLocale;
      localStorage.setItem('user', JSON.stringify(user));
    }
  };

  const handlePayment = (sale: CreditSale) => {
    console.log('Processing payment for sale:', sale._id);
    setSelectedSale(sale);
    setPaymentData({
      paymentAmount: sale.remainingAmount,
      notes: ''
    });
    setIsPaymentDialogOpen(true);
  };

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedSale) return;

    console.log('Payment form submitted:', paymentData);
    setSubmitting(true);
    setError('');

    try {
      const token = localStorage.getItem('auth-token');
      
      const response = await fetch('/api/credit/pay', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          saleId: selectedSale._id,
          paymentAmount: paymentData.paymentAmount,
          notes: paymentData.notes
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process payment');
      }

      console.log('Payment processed successfully');
      setIsPaymentDialogOpen(false);
      setSelectedSale(null);
      setPaymentData({ paymentAmount: 0, notes: '' });
      fetchCreditData(); // Refresh data
    } catch (err: any) {
      console.error('Payment processing error:', err);
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    const currencySymbol = locale === 'ar' ? 'درهم' : 'MAD';
    return `${amount.toFixed(2)} ${currencySymbol}`;
  };

  const formatDate = (date: string) => {
    const dateLocale = locale === 'ar' ? ar : fr;
    return format(new Date(date), 'dd MMM yyyy', { locale: dateLocale });
  };

  const getTotalDebt = () => {
    return customerSummary.reduce((sum, customer) => sum + customer.totalDebt, 0);
  };

  const getDaysOverdue = (saleDate: string) => {
    const today = new Date();
    const sale = new Date(saleDate);
    const diffTime = today.getTime() - sale.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (loading) {
    return (
      <div className="flex h-screen">
        <Navigation locale={locale} onLocaleChange={handleLocaleChange} />
        <div className="flex-1 lg:ml-64 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{messages.common.loading}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50" data-macaly="credit-container">
      <Navigation locale={locale} onLocaleChange={handleLocaleChange} />
      
      {/* Main Content */}
      <div className="flex-1 lg:ml-64" data-macaly="credit-main">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900" data-macaly="credit-title">
                {messages.credit.title}
              </h1>
              <p className="text-gray-600 mt-1">
                Gérez les ventes à crédit et les paiements
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Dette totale</p>
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(getTotalDebt())}
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Clients avec dette</p>
                    <p className="text-2xl font-bold text-gray-900">{customerSummary.length}</p>
                  </div>
                  <div className="w-12 h-12 bg-red-50 rounded-lg flex items-center justify-center">
                    <User className="w-6 h-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Ventes impayées</p>
                    <p className="text-2xl font-bold text-gray-900">{creditSales.length}</p>
                  </div>
                  <div className="w-12 h-12 bg-amber-50 rounded-lg flex items-center justify-center">
                    <CreditCard className="w-6 h-6 text-amber-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Dette totale</p>
                    <p className="text-2xl font-bold text-red-600">
                      {formatCurrency(getTotalDebt())}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-red-50 rounded-lg flex items-center justify-center">
                    <DollarSign className="w-6 h-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Customer Filter */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <Label htmlFor="customer-filter" className="text-sm font-medium">
                  Filtrer par client:
                </Label>
                <Select value={selectedCustomer} onValueChange={setSelectedCustomer}>
                  <SelectTrigger className="w-64">
                    <SelectValue placeholder="Tous les clients" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les clients</SelectItem>
                    {customerSummary.map(customer => (
                      <SelectItem key={customer._id} value={customer._id}>
                        <div className="flex justify-between items-center w-full">
                          <span>{customer.customer.name}</span>
                          <span className="text-red-600 ml-2">
                            {formatCurrency(customer.totalDebt)}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Customer Summary (when no specific customer selected) */}
          {!selectedCustomer && customerSummary.length > 0 && (
            <Card data-macaly="customer-debt-summary">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Résumé des dettes par client
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Client</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Nombre de ventes</TableHead>
                      <TableHead>Dette totale</TableHead>
                      <TableHead>Vente la plus ancienne</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {customerSummary.map((customer) => (
                      <TableRow key={customer._id}>
                        <TableCell>
                          <p className="font-medium">{customer.customer.name}</p>
                        </TableCell>
                        <TableCell>
                          {customer.customer.phone ? (
                            <p className="text-sm text-gray-600">{customer.customer.phone}</p>
                          ) : (
                            <span className="text-gray-500">-</span>
                          )}
                        </TableCell>
                        <TableCell>{customer.salesCount}</TableCell>
                        <TableCell>
                          <span className="font-bold text-red-600">
                            {formatCurrency(customer.totalDebt)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className="text-sm">{formatDate(customer.oldestSale)}</span>
                            <Badge variant="outline" className="text-xs">
                              {getDaysOverdue(customer.oldestSale)} jours
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedCustomer(customer._id)}
                            className="gap-2"
                          >
                            <Eye className="w-4 h-4" />
                            Voir détails
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {/* Credit Sales Table */}
          <Card data-macaly="credit-sales-table">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="w-5 h-5" />
                Ventes à crédit impayées
                {selectedCustomer && (
                  <Badge variant="outline" className="ml-2">
                    {customerSummary.find(c => c._id === selectedCustomer)?.customer.name}
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {creditSales.length === 0 ? (
                <div className="text-center py-12">
                  <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                  <p className="text-gray-600">
                    {selectedCustomer ? 'Ce client n\'a pas de dette' : 'Aucune vente à crédit impayée'}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    Toutes les ventes à crédit ont été payées
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Client</TableHead>
                      <TableHead>Produit</TableHead>
                      <TableHead>Quantité</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Payé</TableHead>
                      <TableHead>Reste</TableHead>
                      <TableHead>Délai</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {creditSales.map((sale) => {
                      const daysOverdue = getDaysOverdue(sale.saleDate);
                      const isOverdue = daysOverdue > 30;
                      
                      return (
                        <TableRow key={sale._id} className={isOverdue ? 'bg-red-50' : ''}>
                          <TableCell className="text-sm">
                            {formatDate(sale.saleDate)}
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">{sale.customerId.name}</p>
                              {sale.customerId.phone && (
                                <p className="text-xs text-gray-500">{sale.customerId.phone}</p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <p className="font-medium">{sale.productId.name}</p>
                          </TableCell>
                          <TableCell>
                            {sale.quantity} {sale.productId.unit}
                          </TableCell>
                          <TableCell className="font-medium">
                            {formatCurrency(sale.totalAmount)}
                          </TableCell>
                          <TableCell className="text-green-600">
                            {formatCurrency(sale.paidAmount)}
                          </TableCell>
                          <TableCell>
                            <span className="font-bold text-red-600">
                              {formatCurrency(sale.remainingAmount)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant={isOverdue ? "destructive" : daysOverdue > 15 ? "secondary" : "default"}
                              className="text-xs"
                            >
                              {daysOverdue} jours
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePayment(sale)}
                              className="gap-2"
                              data-macaly={`pay-credit-${sale._id}`}
                            >
                              <DollarSign className="w-4 h-4" />
                              Payer
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Payment Dialog */}
      <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Enregistrer un paiement</DialogTitle>
            <DialogDescription>
              Paiement pour la vente du {selectedSale && formatDate(selectedSale.saleDate)}
            </DialogDescription>
          </DialogHeader>
          
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {selectedSale && (
            <div className="space-y-4">
              {/* Sale Details */}
              <div className="p-4 bg-gray-50 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Client:</span>
                  <span className="font-medium">{selectedSale.customerId.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Produit:</span>
                  <span className="font-medium">{selectedSale.productId.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Total vente:</span>
                  <span className="font-bold">{formatCurrency(selectedSale.totalAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Déjà payé:</span>
                  <span className="text-green-600">{formatCurrency(selectedSale.paidAmount)}</span>
                </div>
                <div className="flex justify-between border-t pt-2">
                  <span className="text-sm font-medium text-gray-900">Reste à payer:</span>
                  <span className="font-bold text-red-600">{formatCurrency(selectedSale.remainingAmount)}</span>
                </div>
              </div>

              <form onSubmit={handlePaymentSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="paymentAmount">Montant du paiement *</Label>
                  <Input
                    id="paymentAmount"
                    type="number"
                    step="0.01"
                    value={paymentData.paymentAmount}
                    onChange={(e) => setPaymentData(prev => ({ 
                      ...prev, 
                      paymentAmount: Number(e.target.value) 
                    }))}
                    min="0.01"
                    max={selectedSale.remainingAmount}
                    required
                    disabled={submitting}
                  />
                  <p className="text-xs text-gray-600">
                    Maximum: {formatCurrency(selectedSale.remainingAmount)}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Notes (optionnel)</Label>
                  <Textarea
                    id="notes"
                    value={paymentData.notes}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Notes sur le paiement..."
                    rows={2}
                    disabled={submitting}
                  />
                </div>

                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsPaymentDialogOpen(false);
                      setSelectedSale(null);
                      setPaymentData({ paymentAmount: 0, notes: '' });
                    }}
                    disabled={submitting}
                  >
                    Annuler
                  </Button>
                  <Button type="submit" disabled={submitting || paymentData.paymentAmount <= 0}>
                    {submitting ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Traitement...
                      </>
                    ) : (
                      'Enregistrer le paiement'
                    )}
                  </Button>
                </div>
              </form>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
