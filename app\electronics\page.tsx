'use client';

import { useState, useEffect } from 'react';
import ElectronicsLayout from '@/components/electronics/ElectronicsLayout';
import RouteProtection from '@/components/RouteProtection';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Package,
  Users,
  FileText,
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Shield,
  ShoppingCart,
  CreditCard,
  Calendar,
  ArrowUpRight
} from 'lucide-react';
import Link from 'next/link';

interface DashboardStats {
  inventory: {
    totalProducts: number;
    lowStockItems: number;
    totalValue: number;
    categories: number;
  };
  customers: {
    totalCustomers: number;
    newThisMonth: number;
    vipCustomers: number;
    totalCredit: number;
  };
  sales: {
    todayRevenue: number;
    monthlyRevenue: number;
    totalInvoices: number;
    unpaidInvoices: number;
  };
  warranty: {
    activeWarranties: number;
    expiringThisMonth: number;
    expiredWarranties: number;
  };
}

export default function ElectronicsDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [recentActivities, setRecentActivities] = useState([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [statsResponse, activitiesResponse] = await Promise.all([
        fetch('/api/electronics/dashboard/stats'),
        fetch('/api/electronics/dashboard/activities')
      ]);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      if (activitiesResponse.ok) {
        const activitiesData = await activitiesResponse.json();
        setRecentActivities(activitiesData.activities || []);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('fr-FR')} MAD`;
  };

  if (loading) {
    return (
      <ElectronicsLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </ElectronicsLayout>
    );
  }

  return (
    <RouteProtection requiredBusinessModel="electronics">
      <ElectronicsLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Electronics Dashboard</h1>
              <p className="text-gray-600 mt-1">Welcome back! Here's what's happening in your shop.</p>
            </div>
            <div className="flex space-x-3">
              <Button asChild>
                <Link href="/electronics/invoices/new">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  New Sale
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/electronics/inventory/add">
                  <Package className="w-4 h-4 mr-2" />
                  Add Product
                </Link>
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Inventory Stats */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                <Package className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.inventory.totalProducts || 0}</div>
                <div className="flex items-center space-x-2 text-xs text-gray-600">
                  <span>{stats?.inventory.categories || 0} categories</span>
                  {stats?.inventory.lowStockItems && stats.inventory.lowStockItems > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {stats.inventory.lowStockItems} low stock
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Customer Stats */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
                <Users className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.customers.totalCustomers || 0}</div>
                <div className="flex items-center space-x-2 text-xs text-gray-600">
                  <TrendingUp className="h-3 w-3 text-green-500" />
                  <span>+{stats?.customers.newThisMonth || 0} this month</span>
                </div>
              </CardContent>
            </Card>

            {/* Revenue Stats */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(stats?.sales.monthlyRevenue || 0)}</div>
                <div className="flex items-center space-x-2 text-xs text-gray-600">
                  <span>Today: {formatCurrency(stats?.sales.todayRevenue || 0)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Warranty Stats */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Warranties</CardTitle>
                <Shield className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.warranty.activeWarranties || 0}</div>
                <div className="flex items-center space-x-2 text-xs text-gray-600">
                  {stats?.warranty.expiringThisMonth && stats.warranty.expiringThisMonth > 0 && (
                    <Badge variant="outline" className="text-xs">
                      {stats.warranty.expiringThisMonth} expiring soon
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions & Alerts */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Quick Actions */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks and shortcuts</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <Button variant="outline" className="h-20 flex-col" asChild>
                    <Link href="/electronics/invoices/new">
                      <FileText className="h-6 w-6 mb-2" />
                      New Invoice
                    </Link>
                  </Button>
                  <Button variant="outline" className="h-20 flex-col" asChild>
                    <Link href="/electronics/customers/add">
                      <Users className="h-6 w-6 mb-2" />
                      Add Customer
                    </Link>
                  </Button>
                  <Button variant="outline" className="h-20 flex-col" asChild>
                    <Link href="/electronics/inventory">
                      <Package className="h-6 w-6 mb-2" />
                      Check Stock
                    </Link>
                  </Button>
                  <Button variant="outline" className="h-20 flex-col" asChild>
                    <Link href="/electronics/payments">
                      <CreditCard className="h-6 w-6 mb-2" />
                      Record Payment
                    </Link>
                  </Button>
                  <Button variant="outline" className="h-20 flex-col" asChild>
                    <Link href="/electronics/warranty">
                      <Shield className="h-6 w-6 mb-2" />
                      Check Warranty
                    </Link>
                  </Button>
                  <Button variant="outline" className="h-20 flex-col" asChild>
                    <Link href="/electronics/reports">
                      <Calendar className="h-6 w-6 mb-2" />
                      View Reports
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Alerts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
                  Alerts
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {stats?.inventory.lowStockItems && stats.inventory.lowStockItems > 0 && (
                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-red-800">Low Stock Alert</p>
                      <p className="text-xs text-red-600">{stats.inventory.lowStockItems} items need restocking</p>
                    </div>
                    <ArrowUpRight className="h-4 w-4 text-red-600" />
                  </div>
                )}

                {stats?.warranty.expiringThisMonth && stats.warranty.expiringThisMonth > 0 && (
                  <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-orange-800">Warranty Expiring</p>
                      <p className="text-xs text-orange-600">{stats.warranty.expiringThisMonth} warranties expire this month</p>
                    </div>
                    <ArrowUpRight className="h-4 w-4 text-orange-600" />
                  </div>
                )}

                {stats?.sales.unpaidInvoices && stats.sales.unpaidInvoices > 0 && (
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-yellow-800">Unpaid Invoices</p>
                      <p className="text-xs text-yellow-600">{stats.sales.unpaidInvoices} invoices pending payment</p>
                    </div>
                    <ArrowUpRight className="h-4 w-4 text-yellow-600" />
                  </div>
                )}

                {stats?.customers.totalCredit && stats.customers.totalCredit > 0 && (
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-blue-800">Outstanding Credit</p>
                      <p className="text-xs text-blue-600">{formatCurrency(stats.customers.totalCredit)} in credit</p>
                    </div>
                    <ArrowUpRight className="h-4 w-4 text-blue-600" />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </ElectronicsLayout>
    </RouteProtection>
  );
}
