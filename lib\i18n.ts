export const locales = ['fr', 'ar'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'fr';

export function getLocaleFromString(locale: string): Locale {
  if (locales.includes(locale as Locale)) {
    return locale as Locale;
  }
  return defaultLocale;
}

// Translation messages
export const messages = {
  fr: {
    // Navigation
    nav: {
      dashboard: 'Tableau de bord',
      products: 'Produits',
      sales: 'Ventes',
      purchases: 'Achats',
      customers: 'Clients',
      credit: 'Crédit',
      returns: 'Retours',
      analytics: 'Analytics',
      settings: 'Paramètres',
      logout: 'Déconnexion',
      language: 'Langue',
    },
    // Authentication
    auth: {
      login: 'Connexion',
      register: 'Inscription',
      email: 'Email',
      password: 'Mot de passe',
      confirmPassword: 'Confirmer le mot de passe',
      loginButton: 'Se connecter',
      registerButton: "S'inscrire",
      forgotPassword: 'Mot de passe oublié?',
      noAccount: "Vous n'avez pas de compte?",
      hasAccount: 'Vous avez déjà un compte?',
      preferredLanguage: 'Langue préférée',
    },
    // Dashboard
    dashboard: {
      title: 'Tableau de bord',
      todaySales: "Ventes d'aujourd'hui",
      totalProfit: 'Bénéfice total',
      lowStock: 'Stock faible',
      totalCustomers: 'Total clients',
      recentSales: 'Ventes récentes',
      topProducts: 'Produits populaires',
      stockAlerts: 'Alertes de stock',
      import: 'Importer',
      export: 'Exporter',
      refresh: 'Actualiser',
    },
    // Products
    products: {
      title: 'Gestion des produits',
      addProduct: 'Ajouter un produit',
      name: 'Nom du produit',
      unit: 'Unité',
      quantity: 'Quantité',
      buyPrice: "Prix d'achat",
      sellPrice: 'Prix de vente',
      category: 'Catégorie',
      description: 'Description',
      minStock: 'Stock minimum',
      actions: 'Actions',
      edit: 'Modifier',
      delete: 'Supprimer',
      save: 'Enregistrer',
      cancel: 'Annuler',
    },
    // Sales
    sales: {
      title: 'Enregistrer une vente',
      product: 'Produit',
      customer: 'Client',
      quantity: 'Quantité',
      price: 'Prix',
      total: 'Total',
      paymentMethod: 'Mode de paiement',
      cash: 'Espèces',
      credit: 'Crédit',
      card: 'Carte',
      addSale: 'Ajouter la vente',
      salesHistory: 'Historique des ventes',
      date: 'Date',
    },
    // Purchases
    purchases: {
      title: 'Gestion des achats',
      addPurchase: 'Nouvel achat',
      product: 'Produit',
      supplier: 'Fournisseur',
      quantity: 'Quantité',
      buyPrice: 'Prix d\'achat',
      total: 'Total',
      date: 'Date',
      invoice: 'Facture',
      notes: 'Notes',
      purchaseHistory: 'Historique des achats',
    },
    // Customers
    customers: {
      title: 'Gestion des clients',
      addCustomer: 'Ajouter un client',
      newCustomer: 'Nouveau client',
      createCustomer: 'Créer le client',
      addNewCustomer: 'Ajouter un nouveau client',
      createNewCustomerForCredit: 'Créer un nouveau client pour cette vente à crédit',
      createNewCustomerForSales: 'Créer un nouveau client pour suivre vos ventes et gérer la relation client',
      noCustomersFound: 'Aucun client trouvé. Créez un nouveau client pour les ventes à crédit.',
      noCustomersFoundGeneral: 'Aucun client enregistré. Créez un nouveau client pour suivre vos ventes.',
      customerRequired: 'Un client est obligatoire pour les ventes à crédit',
      customerOptional: '(optionnel)',
      noCustomer: 'Aucun client',
      name: 'Nom',
      phone: 'Téléphone',
      email: 'Email',
      address: 'Adresse',
      notes: 'Notes',
      totalDebt: 'Dette totale',
      totalPurchases: 'Achats totaux',
      lastPurchase: 'Dernier achat',
      creating: 'Création...',
    },
    // Credit
    credit: {
      title: 'Gestion des crédits',
      outstandingDebts: 'Dettes en cours',
      markAsPaid: 'Marquer comme payé',
      partialPayment: 'Paiement partiel',
      remainingAmount: 'Montant restant',
      paidAmount: 'Montant payé',
    },
    // Analytics
    analytics: {
      title: 'Analyses et Statistiques',
      subtitle: 'Insights détaillés sur vos performances commerciales',
      period: 'Période d\'analyse',
      lastDays: 'Derniers jours',
      export: 'Exporter',
      refresh: 'Actualiser',
      // KPIs
      kpis: {
        totalSales: 'Ventes totales',
        totalOrders: 'Commandes',
        totalCustomers: 'Clients',
        averageBasket: 'Panier moyen',
        lowStock: 'Stock faible',
        bestProduct: 'Meilleur produit',
        newCustomers: 'Nouveaux clients',
        totalReturns: 'Retours',
      },
      // Charts
      charts: {
        salesTrend: 'Tendance des ventes',
        salesTrendSubtitle: 'Évolution des ventes sur les 30 derniers jours',
        categoryBreakdown: 'Revenus par catégorie',
        categoryBreakdownSubtitle: 'Répartition des revenus par catégories de produits',
        returns: 'Retours',
        returnsSubtitle: 'Commandes retournées sur la période',
      },
      // Tooltips
      tooltips: {
        salesAmount: 'Montant des ventes',
        returnsCount: 'retour',
        returnsCountPlural: 'retours',
        percentageOfTotal: '% du total',
      },
    },
    // Returns
    returns: {
      title: 'Gestion des retours',
      subtitle: 'Gérer les retours de produits et les remboursements',
      addReturn: 'Nouveau retour',
      returnedOrders: 'Retours produits',
      returnedQuantity: 'Quantité retournée',
      returnReason: 'Raison du retour',
      returnRate: 'Taux de retour',
      refundAmount: 'Montant du remboursement',
      originalQuantity: 'Quantité originale',
      originalPrice: 'Prix original',
      returnDate: 'Date de retour',
      processedDate: 'Date de traitement',
      processedBy: 'Traité par',
      status: 'Statut',
      pending: 'En attente',
      approved: 'Approuvé',
      rejected: 'Rejeté',
      notes: 'Notes',
      submitReturn: 'Soumettre le retour',
      processReturn: 'Traiter le retour',
      approveReturn: 'Approuver',
      rejectReturn: 'Rejeter',
      viewReturn: 'Voir le retour',
      editReturn: 'Modifier le retour',
      deleteReturn: 'Supprimer le retour',
      returnHistory: 'Historique des retours',
      noReturns: 'Aucun retour trouvé',
      returnSuccess: 'Retour créé avec succès',
      returnError: 'Erreur lors de la création du retour',
      confirmDelete: 'Êtes-vous sûr de vouloir supprimer ce retour?',
      // Return reasons
      reasons: {
        defective: 'Produit défectueux',
        wrongItem: 'Mauvais article',
        notAsDescribed: 'Non conforme à la description',
        customerChange: 'Changement d\'avis client',
        damaged: 'Produit endommagé',
        expired: 'Produit expiré',
        other: 'Autre raison'
      },
      // Filters
      filterByStatus: 'Filtrer par statut',
      filterByDate: 'Filtrer par date',
      last7Days: '7 derniers jours',
      last30Days: '30 derniers jours',
      last90Days: '90 derniers jours',
      customRange: 'Période personnalisée',
    },
    // Common
    common: {
      add: 'Ajouter',
      edit: 'Modifier',
      delete: 'Supprimer',
      save: 'Enregistrer',
      cancel: 'Annuler',
      search: 'Rechercher',
      select: 'Sélectionner',
      loading: 'Chargement...',
      error: 'Erreur',
      success: 'Succès',
      confirmation: 'Confirmation',
      yes: 'Oui',
      no: 'Non',
      close: 'Fermer',
      currency: 'MAD',
    },
  },
  ar: {
    // Navigation
    nav: {
      dashboard: 'لوحة التحكم',
      products: 'المنتجات',
      sales: 'المبيعات',
      purchases: 'المشتريات',
      customers: 'الزبائن',
      credit: 'الدين',
      returns: 'المرتجعات',
      analytics: 'التحليلات',
      settings: 'الإعدادات',
      logout: 'تسجيل الخروج',
      language: 'اللغة',
    },
    // Authentication
    auth: {
      login: 'تسجيل الدخول',
      register: 'إنشاء حساب',
      email: 'البريد الإلكتروني',
      password: 'كلمة المرور',
      confirmPassword: 'تأكيد كلمة المرور',
      loginButton: 'دخول',
      registerButton: 'إنشاء حساب',
      forgotPassword: 'نسيت كلمة المرور؟',
      noAccount: 'ليس لديك حساب؟',
      hasAccount: 'لديك حساب بالفعل؟',
      preferredLanguage: 'اللغة المفضلة',
    },
    // Dashboard
    dashboard: {
      title: 'لوحة التحكم',
      todaySales: 'مبيعات اليوم',
      totalProfit: 'إجمالي الربح',
      lowStock: 'مخزون منخفض',
      totalCustomers: 'إجمالي الزبائن',
      recentSales: 'المبيعات الأخيرة',
      topProducts: 'المنتجات الأكثر مبيعاً',
      stockAlerts: 'تنبيهات المخزون',
      import: 'استيراد',
      export: 'تصدير',
      refresh: 'تحديث',
    },
    // Products
    products: {
      title: 'إدارة المنتجات',
      addProduct: 'إضافة منتج',
      name: 'اسم المنتج',
      unit: 'الوحدة',
      quantity: 'الكمية',
      buyPrice: 'سعر الشراء',
      sellPrice: 'سعر البيع',
      category: 'الفئة',
      description: 'الوصف',
      minStock: 'الحد الأدنى للمخزون',
      actions: 'الإجراءات',
      edit: 'تعديل',
      delete: 'حذف',
      save: 'حفظ',
      cancel: 'إلغاء',
    },
    // Sales
    sales: {
      title: 'تسجيل مبيعة',
      product: 'المنتج',
      customer: 'الزبون',
      quantity: 'الكمية',
      price: 'السعر',
      total: 'الإجمالي',
      paymentMethod: 'طريقة الدفع',
      cash: 'نقداً',
      credit: 'بالدين',
      card: 'بالبطاقة',
      addSale: 'إضافة المبيعة',
      salesHistory: 'تاريخ المبيعات',
      date: 'التاريخ',
    },
    // Purchases
    purchases: {
      title: 'تدبير المشتريات',
      addPurchase: 'شراء جديد',
      product: 'المنتوج',
      supplier: 'المورد',
      quantity: 'الكمية',
      buyPrice: 'ثمن الشراء',
      total: 'المجموع',
      date: 'التاريخ',
      invoice: 'الفاتورة',
      notes: 'ملاحظات',
      purchaseHistory: 'تاريخ المشتريات',
    },
    // Customers
    customers: {
      title: 'إدارة الزبائن',
      addCustomer: 'إضافة زبون',
      newCustomer: 'زبون جديد',
      createCustomer: 'إنشاء الزبون',
      addNewCustomer: 'إضافة زبون جديد',
      createNewCustomerForCredit: 'إنشاء زبون جديد لهذه المبيعة بالدين',
      createNewCustomerForSales: 'إنشاء زبون جديد لتتبع مبيعاتك وإدارة علاقة الزبائن',
      noCustomersFound: 'لم يتم العثور على زبائن. أنشئ زبون جديد للمبيعات بالدين.',
      noCustomersFoundGeneral: 'لا يوجد زبائن مسجلين. أنشئ زبون جديد لتتبع مبيعاتك.',
      customerRequired: 'الزبون مطلوب للمبيعات بالدين',
      customerOptional: '(اختياري)',
      noCustomer: 'بدون زبون',
      name: 'الاسم',
      phone: 'الهاتف',
      email: 'البريد الإلكتروني',
      address: 'العنوان',
      notes: 'ملاحظات',
      totalDebt: 'إجمالي الدين',
      totalPurchases: 'إجمالي المشتريات',
      lastPurchase: 'آخر شراء',
      creating: 'جاري الإنشاء...',
    },
    // Analytics
    analytics: {
      title: 'التحليلات والإحصائيات',
      subtitle: 'رؤى مفصلة حول أداء عملك التجاري',
      period: 'فترة التحليل',
      lastDays: 'الأيام الماضية',
      export: 'تصدير',
      refresh: 'تحديث',
      // KPIs
      kpis: {
        totalSales: 'إجمالي المبيعات',
        totalOrders: 'الطلبات',
        totalCustomers: 'الزبائن',
        averageBasket: 'متوسط السلة',
        lowStock: 'مخزون منخفض',
        bestProduct: 'أفضل منتج',
        newCustomers: 'زبائن جدد',
        totalReturns: 'المرتجعات',
      },
      // Charts
      charts: {
        salesTrend: 'اتجاه المبيعات',
        salesTrendSubtitle: 'تطور المبيعات خلال الـ 30 يوماً الماضية',
        categoryBreakdown: 'الإيرادات حسب الفئة',
        categoryBreakdownSubtitle: 'توزيع الإيرادات حسب فئات المنتجات',
        returns: 'المرتجعات',
        returnsSubtitle: 'الطلبات المرتجعة خلال الفترة',
      },
      // Tooltips
      tooltips: {
        salesAmount: 'مبلغ المبيعات',
        returnsCount: 'مرتجع',
        returnsCountPlural: 'مرتجعات',
        percentageOfTotal: '% من الإجمالي',
      },
    },
    // Returns
    returns: {
      title: 'إدارة المرتجعات',
      subtitle: 'إدارة مرتجعات المنتجات والمبالغ المستردة',
      addReturn: 'مرتجع جديد',
      returnedOrders: 'البرودويات المرجوعين',
      returnedQuantity: 'الكمية المرجوعة',
      returnReason: 'علاش رجعتيه',
      returnRate: 'نسبة الرجوع',
      refundAmount: 'مبلغ الاسترداد',
      originalQuantity: 'الكمية الأصلية',
      originalPrice: 'السعر الأصلي',
      returnDate: 'تاريخ الإرجاع',
      processedDate: 'تاريخ المعالجة',
      processedBy: 'تمت المعالجة بواسطة',
      status: 'الحالة',
      pending: 'في الانتظار',
      approved: 'موافق عليه',
      rejected: 'مرفوض',
      notes: 'ملاحظات',
      submitReturn: 'إرسال الإرجاع',
      processReturn: 'معالجة الإرجاع',
      approveReturn: 'موافقة',
      rejectReturn: 'رفض',
      viewReturn: 'عرض الإرجاع',
      editReturn: 'تعديل الإرجاع',
      deleteReturn: 'حذف الإرجاع',
      returnHistory: 'تاريخ المرتجعات',
      noReturns: 'لا توجد مرتجعات',
      returnSuccess: 'تم إنشاء الإرجاع بنجاح',
      returnError: 'خطأ في إنشاء الإرجاع',
      confirmDelete: 'هل أنت متأكد من حذف هذا الإرجاع؟',
      // Return reasons
      reasons: {
        defective: 'منتج معيب',
        wrongItem: 'منتج خاطئ',
        notAsDescribed: 'غير مطابق للوصف',
        customerChange: 'تغيير رأي العميل',
        damaged: 'منتج تالف',
        expired: 'منتج منتهي الصلاحية',
        other: 'سبب آخر'
      },
      // Filters
      filterByStatus: 'تصفية حسب الحالة',
      filterByDate: 'تصفية حسب التاريخ',
      last7Days: 'آخر 7 أيام',
      last30Days: 'آخر 30 يوم',
      last90Days: 'آخر 90 يوم',
      customRange: 'فترة مخصصة',
    },
    // Credit
    credit: {
      title: 'إدارة الديون',
      outstandingDebts: 'الديون المعلقة',
      markAsPaid: 'تحديد كمدفوع',
      partialPayment: 'دفع جزئي',
      remainingAmount: 'المبلغ المتبقي',
      paidAmount: 'المبلغ المدفوع',
    },
    // Common
    common: {
      add: 'إضافة',
      edit: 'تعديل',
      delete: 'حذف',
      save: 'حفظ',
      cancel: 'إلغاء',
      search: 'بحث',
      select: 'اختر',
      loading: 'جاري التحميل...',
      error: 'خطأ',
      success: 'نجح',
      confirmation: 'تأكيد',
      yes: 'نعم',
      no: 'لا',
      close: 'إغلاق',
      currency: 'درهم',
    },
  },
} as const;

export type Messages = typeof messages.fr;

export function getMessages(locale: Locale): any {
  return messages[locale];
}