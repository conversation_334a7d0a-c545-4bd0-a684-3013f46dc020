'use client';

import { useState, useEffect } from 'react';
import {
  BarChart3,
  Download,
  Calendar,
  DollarSign,
  ShoppingBag,
  Users
} from 'lucide-react';

interface ReportData {
  salesByMonth: Array<{ month: string; sales: number; revenue: number }>;
  topProducts: Array<{ name: string; quantity: number; revenue: number }>;
  customerStats: {
    totalCustomers: number;
    newCustomers: number;
    returningCustomers: number;
  };
  inventoryStats: {
    totalProducts: number;
    lowStock: number;
    outOfStock: number;
  };
}

export default function ReportsPage() {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('last-30-days');

  useEffect(() => {
    // Simulate loading report data
    setTimeout(() => {
      setReportData({
        salesByMonth: [
          { month: 'Jan 2024', sales: 45, revenue: 12500 },
          { month: 'Feb 2024', sales: 52, revenue: 15200 },
          { month: 'Mar 2024', sales: 38, revenue: 11800 },
          { month: 'Apr 2024', sales: 61, revenue: 18900 },
          { month: 'May 2024', sales: 47, revenue: 14300 },
          { month: 'Jun 2024', sales: 55, revenue: 16700 }
        ],
        topProducts: [
          { name: 'iPhone 15 Pro', quantity: 25, revenue: 24975 },
          { name: 'Samsung Galaxy S24', quantity: 18, revenue: 16182 },
          { name: 'MacBook Air', quantity: 12, revenue: 15588 },
          { name: 'iPad Pro', quantity: 15, revenue: 11985 },
          { name: 'AirPods Pro', quantity: 32, revenue: 7968 }
        ],
        customerStats: {
          totalCustomers: 156,
          newCustomers: 23,
          returningCustomers: 89
        },
        inventoryStats: {
          totalProducts: 245,
          lowStock: 12,
          outOfStock: 3
        }
      });
      setLoading(false);
    }, 1000);
  }, [selectedPeriod]);

  const periodOptions = [
    { value: 'last-7-days', label: 'Last 7 Days' },
    { value: 'last-30-days', label: 'Last 30 Days' },
    { value: 'last-3-months', label: 'Last 3 Months' },
    { value: 'last-6-months', label: 'Last 6 Months' },
    { value: 'last-year', label: 'Last Year' }
  ];

  const exportReport = (format: 'pdf' | 'excel') => {
    // Simulate export functionality
    alert(`Exporting report as ${format.toUpperCase()}...`);
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600">Business insights and performance metrics</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => exportReport('pdf')}
            className="bg-red-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-red-700"
          >
            <Download className="h-5 w-5 mr-2" />
            Export PDF
          </button>
          <button
            onClick={() => exportReport('excel')}
            className="bg-green-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-green-700"
          >
            <Download className="h-5 w-5 mr-2" />
            Export Excel
          </button>
        </div>
      </div>

      {/* Period Selector */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="flex items-center gap-4">
          <Calendar className="h-5 w-5 text-gray-400" />
          <label className="text-sm font-medium text-gray-700">Report Period:</label>
          <select
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
          >
            {periodOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                ${reportData?.salesByMonth.reduce((sum, month) => sum + month.revenue, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <ShoppingBag className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Sales</p>
              <p className="text-2xl font-bold text-gray-900">
                {reportData?.salesByMonth.reduce((sum, month) => sum + month.sales, 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-purple-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Customers</p>
              <p className="text-2xl font-bold text-gray-900">{reportData?.customerStats.totalCustomers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 text-orange-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Sale Value</p>
              <p className="text-2xl font-bold text-gray-900">
                ${reportData ? Math.round(
                  reportData.salesByMonth.reduce((sum, month) => sum + month.revenue, 0) /
                  reportData.salesByMonth.reduce((sum, month) => sum + month.sales, 0)
                ) : 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Sales by Month Chart */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sales by Month</h3>
          <div className="space-y-4">
            {reportData?.salesByMonth.map((month, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium text-gray-900">{month.month}</span>
                    <span className="text-gray-600">{month.sales} sales</span>
                  </div>
                  <div className="mt-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{
                        width: `${(month.revenue / Math.max(...reportData.salesByMonth.map(m => m.revenue))) * 100}%`
                      }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">${month.revenue.toLocaleString()}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Products</h3>
          <div className="space-y-4">
            {reportData?.topProducts.map((product, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium text-gray-900">{product.name}</span>
                    <span className="text-gray-600">{product.quantity} sold</span>
                  </div>
                  <div className="mt-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{
                        width: `${(product.revenue / Math.max(...reportData.topProducts.map(p => p.revenue))) * 100}%`
                      }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">${product.revenue.toLocaleString()}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Stats */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Analytics</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Customers</span>
              <span className="font-semibold text-gray-900">{reportData?.customerStats.totalCustomers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">New Customers</span>
              <span className="font-semibold text-green-600">+{reportData?.customerStats.newCustomers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Returning Customers</span>
              <span className="font-semibold text-blue-600">{reportData?.customerStats.returningCustomers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Customer Retention Rate</span>
              <span className="font-semibold text-purple-600">
                {reportData ? Math.round((reportData.customerStats.returningCustomers / reportData.customerStats.totalCustomers) * 100) : 0}%
              </span>
            </div>
          </div>
        </div>

        {/* Inventory Stats */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Inventory Status</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Products</span>
              <span className="font-semibold text-gray-900">{reportData?.inventoryStats.totalProducts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Low Stock Items</span>
              <span className="font-semibold text-yellow-600">{reportData?.inventoryStats.lowStock}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Out of Stock</span>
              <span className="font-semibold text-red-600">{reportData?.inventoryStats.outOfStock}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Stock Health</span>
              <span className="font-semibold text-green-600">
                {reportData ? Math.round(((reportData.inventoryStats.totalProducts - reportData.inventoryStats.lowStock - reportData.inventoryStats.outOfStock) / reportData.inventoryStats.totalProducts) * 100) : 0}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
