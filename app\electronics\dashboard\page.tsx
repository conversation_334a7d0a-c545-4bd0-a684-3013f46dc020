'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  ShoppingBagIcon, 
  CubeIcon, 
  UserGroupIcon, 
  ChartBarIcon,
  WrenchScrewdriverIcon,
  ShieldCheckIcon,
  BanknotesIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

export default function ElectronicsDashboard() {
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalCustomers: 0,
    totalSales: 0,
    pendingReturns: 0,
    activeWarranties: 0,
    lowStock: 0
  });

  const quickActions = [
    {
      title: 'Inventory Management',
      description: 'Manage electronics inventory',
      href: '/electronics/inventory',
      icon: CubeIcon,
      color: 'bg-blue-500'
    },
    {
      title: 'Sales & Invoices',
      description: 'Process sales and generate invoices',
      href: '/electronics/sales',
      icon: ShoppingBagIcon,
      color: 'bg-green-500'
    },
    {
      title: 'Customer Management',
      description: 'Manage customer database',
      href: '/electronics/customers',
      icon: UserGroupIcon,
      color: 'bg-purple-500'
    },
    {
      title: 'Returns & Exchanges',
      description: 'Handle product returns',
      href: '/electronics/returns',
      icon: WrenchScrewdriverIcon,
      color: 'bg-orange-500'
    },
    {
      title: 'Warranty Tracking',
      description: 'Track product warranties',
      href: '/electronics/warranty',
      icon: ShieldCheckIcon,
      color: 'bg-indigo-500'
    },
    {
      title: 'Reports & Analytics',
      description: 'View business analytics',
      href: '/electronics/reports',
      icon: ChartBarIcon,
      color: 'bg-red-500'
    }
  ];

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Electronics CRM Dashboard</h1>
        <p className="text-gray-600 mt-2">Manage your electronics shop efficiently</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <CubeIcon className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Products</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <UserGroupIcon className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Customers</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCustomers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <BanknotesIcon className="h-8 w-8 text-purple-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Sales</p>
              <p className="text-2xl font-bold text-gray-900">${stats.totalSales}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quickActions.map((action, index) => (
            <Link
              key={index}
              href={action.href}
              className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <div className="flex items-center mb-4">
                <div className={`p-3 rounded-lg ${action.color}`}>
                  <action.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{action.title}</h3>
              <p className="text-gray-600">{action.description}</p>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
