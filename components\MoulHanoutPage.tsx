'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import {
  Store,
  Users,
  Package,
  CreditCard,
  BarChart3,
  ArrowRight,
  ArrowLeft,
  ShoppingCart,
  AlertTriangle,
  RefreshCw,
  CheckCircle,
  Globe
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function MoulHanoutPage() {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const isRTL = locale === 'ar';

  const switchLanguage = (newLocale: string) => {
    const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);
    router.push(newPath);
  };

  const handleBackToHome = () => {
    router.push(`/${locale}`);
  };

  const handleTryNow = () => {
    router.push(`/${locale}/register?businessModel=general`);
  };

  const handleViewDemo = () => {
    router.push(`/${locale}/login?demo=moul-hanout`);
  };

  return (
    <div className={`min-h-screen bg-white ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Back Button */}
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={handleBackToHome}
                className="flex items-center gap-2"
              >
                {isRTL ? <ArrowRight className="w-4 h-4" /> : <ArrowLeft className="w-4 h-4" />}
                {t('nav.home')}
              </Button>

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-green-600 to-red-600 rounded-lg flex items-center justify-center">
                  <Store className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-gray-900">{t('moulHanout.title')}</h1>
                </div>
              </div>
            </div>

            {/* Language Switcher */}
            <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
              <Button
                variant={locale === 'fr' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => switchLanguage('fr')}
                className="text-xs"
              >
                FR
              </Button>
              <Button
                variant={locale === 'ar' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => switchLanguage('ar')}
                className="text-xs"
              >
                AR
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-red-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            <div className="w-24 h-24 bg-gradient-to-br from-green-500 to-red-500 rounded-3xl flex items-center justify-center mx-auto">
              <Store className="w-12 h-12 text-white" />
            </div>

            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                {t('moulHanout.title')}
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('moulHanout.subtitle')}
              </p>
              <p className="text-lg text-gray-600 max-w-4xl mx-auto">
                {t('moulHanout.description')}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                onClick={handleTryNow}
              >
                {t('moulHanout.tryNow')}
                <ArrowRight className={`${isRTL ? 'mr-2' : 'ml-2'} h-5 w-5`} />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-gray-300 px-8 py-4 text-lg font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200"
                onClick={handleViewDemo}
              >
                {t('moulHanout.viewDemo')}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('moulHanout.features.title')}
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Sales Management */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto">
                  <ShoppingCart className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{t('moulHanout.features.sales')}</h3>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {t('moulHanout.features.salesDesc')}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Inventory Management */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto">
                  <Package className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{t('moulHanout.features.inventory')}</h3>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {t('moulHanout.features.inventoryDesc')}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Customer Management */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{t('moulHanout.features.customers')}</h3>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {t('moulHanout.features.customersDesc')}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Returns Management */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
              <CardContent className="p-6 text-center space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto">
                  <RefreshCw className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{t('moulHanout.features.returns')}</h3>
                  <p className="text-sm text-gray-500 leading-relaxed">
                    {t('moulHanout.features.returnsDesc')}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Screenshots/Demo Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {locale === 'ar' ? 'شوف النظام كيف خدام' : 'Découvrez l\'interface'}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {locale === 'ar'
                ? 'واجهة بسيطة ومفهومة، مصممة خصيصا للبقالات المغربية'
                : 'Interface simple et intuitive, conçue pour les épiceries marocaines'
              }
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Dashboard Preview */}
            <div className="space-y-4">
              <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 aspect-[4/3] flex items-center justify-center border border-green-200">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-green-600 rounded-2xl flex items-center justify-center mx-auto">
                    <BarChart3 className="w-8 h-8 text-white" />
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-green-300 rounded w-24 mx-auto"></div>
                    <div className="h-2 bg-green-200 rounded w-16 mx-auto"></div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {locale === 'ar' ? 'لوحة التحكم' : 'Tableau de bord'}
                </h3>
                <p className="text-sm text-gray-500">
                  {locale === 'ar'
                    ? 'شوف مبيعات اليوم والأرباح في نظرة واحدة'
                    : 'Visualisez vos ventes du jour et bénéfices en un coup d\'œil'
                  }
                </p>
              </div>
            </div>

            {/* Sales Preview */}
            <div className="space-y-4">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 aspect-[4/3] flex items-center justify-center border border-blue-200">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto">
                    <ShoppingCart className="w-8 h-8 text-white" />
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-blue-300 rounded w-28 mx-auto"></div>
                    <div className="h-2 bg-blue-200 rounded w-20 mx-auto"></div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {locale === 'ar' ? 'إدارة المبيعات' : 'Gestion des ventes'}
                </h3>
                <p className="text-sm text-gray-500">
                  {locale === 'ar'
                    ? 'سجل مبيعاتك بسرعة ودقة'
                    : 'Enregistrez vos ventes rapidement et précisément'
                  }
                </p>
              </div>
            </div>

            {/* Customers Preview */}
            <div className="space-y-4">
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-8 aspect-[4/3] flex items-center justify-center border border-purple-200">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-purple-600 rounded-2xl flex items-center justify-center mx-auto">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-purple-300 rounded w-32 mx-auto"></div>
                    <div className="h-2 bg-purple-200 rounded w-24 mx-auto"></div>
                  </div>
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {locale === 'ar' ? 'إدارة الزبائن' : 'Gestion des clients'}
                </h3>
                <p className="text-sm text-gray-500">
                  {locale === 'ar'
                    ? 'تتبع زبائنك وديونهم بسهولة'
                    : 'Suivez vos clients et leurs dettes facilement'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-br from-green-600 to-red-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8 text-white">
            <div className="space-y-4">
              <h2 className="text-3xl md:text-4xl font-bold">
                {locale === 'ar' ? 'ابدا دبا مجانا' : 'Commencez gratuitement maintenant'}
              </h2>
              <p className="text-xl opacity-90 max-w-2xl mx-auto">
                {locale === 'ar'
                  ? 'انضم لآلاف التجار المغاربة اللي كيستعملو النظام ديالنا'
                  : 'Rejoignez des milliers de commerçants marocains qui utilisent notre système'
                }
              </p>
            </div>

            <div className="flex flex-wrap justify-center items-center gap-6 text-sm opacity-90">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                <span>{locale === 'ar' ? 'مجاني 100%' : '100% gratuit'}</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                <span>{locale === 'ar' ? 'بدون رسوم خفية' : 'Aucun frais caché'}</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4" />
                <span>{locale === 'ar' ? 'دعم باللغة العربية' : 'Support en arabe'}</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="bg-white text-green-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                onClick={handleTryNow}
              >
                {t('moulHanout.tryNow')}
                <ArrowRight className={`${isRTL ? 'mr-2' : 'ml-2'} h-5 w-5`} />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-white text-white hover:bg-white hover:text-green-600 px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200"
                onClick={handleViewDemo}
              >
                {t('moulHanout.viewDemo')}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <div className="flex items-center justify-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-red-600 rounded-lg flex items-center justify-center">
                <Store className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold">{t('homepage.title')}</h3>
                <p className="text-sm text-gray-400">{t('homepage.subtitle')}</p>
              </div>
            </div>

            <div className="border-t border-gray-800 pt-6">
              <p className="text-sm text-gray-500">
                © 2024 {t('homepage.title')}. {locale === 'ar' ? 'جميع الحقوق محفوظة' : 'Tous droits réservés'}
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
