// Simple test file to verify phone number validation
// Run with: node test-phone-validation.js

// Phone validation function (copied from lib/auth.ts)
function isValidPhone(phone) {
  const phoneRegex = /^(\+212|0)[5-7][0-9]{8}$/;
  return phoneRegex.test(phone);
}

// Phone normalization function (copied from lib/auth.ts)
function normalizePhone(phone) {
  // Remove all spaces and dashes
  const cleaned = phone.replace(/[\s-]/g, '');
  
  // Convert 0XXXXXXXXX to +212XXXXXXXXX
  if (cleaned.startsWith('0') && cleaned.length === 10) {
    return '+212' + cleaned.substring(1);
  }
  
  // If already starts with +212, return as is
  if (cleaned.startsWith('+212')) {
    return cleaned;
  }
  
  return cleaned;
}

// Test cases
const testCases = [
  // Valid Moroccan phone numbers
  { input: '0612345678', expected: true, description: 'Valid format: 06XXXXXXXX' },
  { input: '0712345678', expected: true, description: 'Valid format: 07XXXXXXXX' },
  { input: '0512345678', expected: true, description: 'Valid format: 05XXXXXXXX' },
  { input: '+212612345678', expected: true, description: 'Valid format: +212XXXXXXXXX' },
  { input: '06 12 34 56 78', expected: true, description: 'Valid with spaces (normalized)' },
  { input: '06-12-34-56-78', expected: true, description: 'Valid with dashes (normalized)' },
  
  // Invalid phone numbers
  { input: '0412345678', expected: false, description: 'Invalid: starts with 04' },
  { input: '0812345678', expected: false, description: 'Invalid: starts with 08' },
  { input: '061234567', expected: false, description: 'Invalid: too short' },
  { input: '06123456789', expected: false, description: 'Invalid: too long' },
  { input: '+33612345678', expected: false, description: 'Invalid: French number' },
  { input: 'abc1234567', expected: false, description: 'Invalid: contains letters' },
  { input: '', expected: false, description: 'Invalid: empty string' },
];

console.log('🧪 Testing Moroccan Phone Number Validation\n');

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  const normalized = normalizePhone(testCase.input);
  const isValid = isValidPhone(normalized);
  const success = isValid === testCase.expected;
  
  console.log(`Test ${index + 1}: ${testCase.description}`);
  console.log(`  Input: "${testCase.input}"`);
  console.log(`  Normalized: "${normalized}"`);
  console.log(`  Valid: ${isValid}`);
  console.log(`  Expected: ${testCase.expected}`);
  console.log(`  Result: ${success ? '✅ PASS' : '❌ FAIL'}\n`);
  
  if (success) {
    passed++;
  } else {
    failed++;
  }
});

console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);

if (failed === 0) {
  console.log('🎉 All tests passed! Phone validation is working correctly.');
} else {
  console.log('⚠️  Some tests failed. Please check the validation logic.');
}
