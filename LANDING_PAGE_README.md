# CRM الحانوت - Landing Page

## Overview
A modern, mobile-friendly landing page for a CRM application designed specifically for small Moroccan businesses such as grocery shops (Moul Hanout), hardware stores, and local boutiques.

## Features

### 🎯 Target Audience
- Moroccan shop owners (non-tech-savvy)
- French-speaking or Darija-speaking users
- People managing products, clients, payments, and stock manually

### 🏷️ Page Sections

1. **Hero Section**
   - Clear, bold headline in French and Darija (Arabic script)
   - Subheading explaining the CRM's value
   - CTA: "Essayer gratuitement" / "جرب مجانا"

2. **Features Section** (Icons + short text)
   - إدارة الزبائن → Gérer vos clients facilement
   - تتبع الديون → Suivi des paiements et dettes
   - إحصائيات المبيعات → Statistiques des produits et ventes
   - استيراد/تصدير البيانات → Exporter/Importer vos données CSV/TXT

3. **Screenshots Section**
   - 3 mockup screenshots with captions
   - Dashboard, Customer Management, Sales Tracking

4. **Testimonials Section**
   - 3 testimonials from Moroccan shop owners
   - In both Darija and French

5. **Call-to-Action Section**
   - "Commencez aujourd'hui – Gratuit et simple"
   - Signup form with name, phone, and email

### 📱 Design Features
- Clean, intuitive, and localized
- Moroccan-style colors (green, red, white)
- Icons for simplicity
- Fully responsive (mobile-first)
- Arabic text support with proper RTL handling

### 🈳 Language Support
- **French** + **Darija (Arabic script)**
- Prioritizes clarity over marketing buzzwords
- Natural, spoken Moroccan dialect translations

## Technical Implementation

### Components Used
- Next.js 14 with TypeScript
- Tailwind CSS for styling
- Lucide React for icons
- Shadcn/ui components

### Key Files
- `app/page.tsx` - Main landing page component
- `app/layout.tsx` - Updated with Arabic support and SEO metadata
- `app/globals.css` - Custom CSS for Arabic text and Moroccan styling

### Styling Features
- Custom Moroccan gradient colors
- Arabic text optimization
- Smooth animations and hover effects
- Mobile-first responsive design
- Accessibility improvements

## Usage

The landing page automatically redirects users to:
- `/register` when they click "Get Started" buttons
- `/login` when they click "Login" buttons
- Pre-fills registration form with data from the landing page form

## Customization

### Colors
The page uses Moroccan flag colors:
- Green: `#16a34a`
- Red: `#dc2626`
- White: `#ffffff`

### Typography
- Arabic text uses optimized font stack
- Proper line-height and letter-spacing for readability
- Responsive text sizing

### Content
All text content is bilingual (French/Darija) and can be easily modified in the component file.
