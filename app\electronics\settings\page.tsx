'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import {
  Settings,
  Store,
  Bell,
  Shield,
  DollarSign,
  Printer,
  FileText,
  Upload,
  Eye,
  Download,
  QrCode,
  Globe,
  Lock,
  Image,
  Layout,
  Palette,
  Building,
  Hash
} from 'lucide-react';

interface ShopSettings {
  shopName: string;
  address: string;
  phone: string;
  email: string;
  taxRate: number;
  currency: string;
  lowStockThreshold: number;
  enableNotifications: boolean;
  enableWarrantyTracking: boolean;
  defaultWarrantyPeriod: number;
  receiptTemplate: string;
  autoBackup: boolean;
}

interface InvoiceSettings {
  // Company Information (Moroccan Standards)
  companyName: string;
  companyAddress: string;
  ice: string; // Identifiant Commun de l'Entreprise
  rc: string; // Registre de Commerce
  cnss: string; // CNSS (if applicable)
  taxId: string; // IF (Identifiant Fiscal)
  phone: string;
  email: string;
  website: string;

  // Logo and Branding
  logo: string | null;
  logoPosition: 'left' | 'center' | 'right';

  // Layout and Design
  template: 'simple' | 'compact' | 'signature';
  primaryColor: string;
  secondaryColor: string;
  fontSize: 'small' | 'medium' | 'large';

  // Formatting
  currency: string;
  locale: 'fr-MA' | 'ar-MA' | 'en-US';
  dateFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';

  // Features
  enableQrCode: boolean;
  qrCodeContent: 'invoice_url' | 'company_info' | 'payment_info';
  autoGenerateNumber: boolean;
  invoicePrefix: string;
  invoiceCounter: number;

  // Multi-language
  defaultLanguage: 'fr' | 'ar' | 'en';
  enableMultiLanguage: boolean;

  // Footer and Terms
  footerText: string;
  termsAndConditions: string;
  bankDetails: string;

  // Signature
  enableSignature: boolean;
  signatureText: string;
}

export default function SettingsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(false);
  const [saved, setSaved] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewInvoice, setPreviewInvoice] = useState(false);

  const [settings, setSettings] = useState<ShopSettings>({
    shopName: 'Electronics Pro Shop',
    address: '123 Mohammed V Street, Casablanca',
    phone: '+************',
    email: '<EMAIL>',
    taxRate: 20,
    currency: 'MAD',
    lowStockThreshold: 5,
    enableNotifications: true,
    enableWarrantyTracking: true,
    defaultWarrantyPeriod: 12,
    receiptTemplate: 'standard',
    autoBackup: true
  });

  const [invoiceSettings, setInvoiceSettings] = useState<InvoiceSettings>({
    // Company Information
    companyName: 'Electronics Pro Shop',
    companyAddress: '123 Avenue Mohammed V, Quartier Maarif\n20000 Casablanca, Maroc',
    ice: '0*************3',
    rc: '12345',
    cnss: '1234567',
    taxId: 'IF12345678',
    phone: '+*********** 456',
    email: '<EMAIL>',
    website: 'www.electronicspro.ma',

    // Logo and Branding
    logo: null,
    logoPosition: 'left',

    // Layout and Design
    template: 'simple',
    primaryColor: '#2563eb',
    secondaryColor: '#64748b',
    fontSize: 'medium',

    // Formatting
    currency: 'MAD',
    locale: 'fr-MA',
    dateFormat: 'DD/MM/YYYY',

    // Features
    enableQrCode: true,
    qrCodeContent: 'invoice_url',
    autoGenerateNumber: true,
    invoicePrefix: 'FAC',
    invoiceCounter: 1001,

    // Multi-language
    defaultLanguage: 'fr',
    enableMultiLanguage: true,

    // Footer and Terms
    footerText: 'Merci pour votre confiance - شكرا لثقتكم',
    termsAndConditions: 'Conditions de vente disponibles sur demande.\nPaiement à 30 jours.\nTout retard de paiement entraînera des pénalités.',
    bankDetails: 'Banque: Attijariwafa Bank\nRIB: 007 123 ************* 34\nSWIFT: BCMAMAMC',

    // Signature
    enableSignature: false,
    signatureText: 'Signature et cachet'
  });

  const handleSave = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with actual API
      const payload = {
        settings,
        invoiceSettings
      };
      console.log('Saving settings:', payload);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSaved(true);
      setTimeout(() => setSaved(false), 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Error saving settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ShopSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleInvoiceInputChange = (field: keyof InvoiceSettings, value: any) => {
    setInvoiceSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) { // 2MB limit
        alert('Logo file size must be less than 2MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setInvoiceSettings(prev => ({
          ...prev,
          logo: result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const generateInvoiceNumber = () => {
    const { invoicePrefix, invoiceCounter } = invoiceSettings;
    const year = new Date().getFullYear();
    return `${invoicePrefix}-${year}-${String(invoiceCounter).padStart(4, '0')}`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(invoiceSettings.locale, {
      style: 'currency',
      currency: invoiceSettings.currency
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    };

    if (invoiceSettings.locale === 'fr-MA') {
      return date.toLocaleDateString('fr-FR', options);
    } else if (invoiceSettings.locale === 'ar-MA') {
      return date.toLocaleDateString('ar-MA', options);
    }
    return date.toLocaleDateString('en-US', options);
  };

  const generateQRCode = (content: string) => {
    // In a real implementation, you would use a QR code library
    // For now, we'll return a placeholder
    return `https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=${encodeURIComponent(content)}`;
  };

  const tabs = [
    { id: 'general', label: 'Général', icon: Settings },
    { id: 'invoice', label: 'Factures', icon: FileText },
    { id: 'permissions', label: 'Permissions', icon: Lock }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Paramètres</h1>
          <p className="text-gray-600">Configurez les paramètres de votre magasin d'électronique</p>
        </div>
        <div className="flex items-center space-x-3">
          {activeTab === 'invoice' && (
            <button
              onClick={() => setPreviewInvoice(true)}
              className="px-4 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50"
            >
              <Eye className="h-5 w-5 mr-2" />
              Aperçu
            </button>
          )}
          <button
            onClick={handleSave}
            disabled={loading}
            className={`px-4 py-2 rounded-lg flex items-center ${loading
              ? 'bg-gray-400 cursor-not-allowed'
              : saved
                ? 'bg-green-600 hover:bg-green-700'
                : 'bg-blue-600 hover:bg-blue-700'
              } text-white`}
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Settings className="h-5 w-5 mr-2" />
            )}
            {loading ? 'Enregistrement...' : saved ? 'Enregistré!' : 'Enregistrer'}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'general' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Shop Information */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Store className="h-6 w-6 text-blue-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Informations du Magasin</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nom du Magasin</label>
                <input
                  type="text"
                  value={settings.shopName}
                  onChange={(e) => handleInputChange('shopName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Adresse</label>
                <textarea
                  value={settings.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                <input
                  type="tel"
                  value={settings.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  value={settings.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Financial Settings */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <DollarSign className="h-6 w-6 text-green-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Paramètres Financiers</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Taux de TVA (%)</label>
                <input
                  type="number"
                  value={settings.taxRate}
                  onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value))}
                  min="0"
                  max="100"
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Devise</label>
                <select
                  value={settings.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="MAD">Dirham Marocain (MAD)</option>
                  <option value="USD">Dollar US (USD)</option>
                  <option value="EUR">Euro (EUR)</option>
                </select>
              </div>
            </div>
          </div>

          {/* Invoice Customization */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <FileText className="h-6 w-6 text-blue-500 mr-2" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Personnalisation des Factures</h3>
                  <p className="text-sm text-gray-600">Configurez vos factures selon les normes marocaines</p>
                </div>
              </div>
              <button
                onClick={() => router.push('/electronics/settings/invoice')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
              >
                <Settings className="h-4 w-4 mr-2" />
                Configurer
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center text-gray-600">
                <Building className="h-4 w-4 mr-2" />
                Informations entreprise
              </div>
              <div className="flex items-center text-gray-600">
                <Image className="h-4 w-4 mr-2" />
                Logo et branding
              </div>
              <div className="flex items-center text-gray-600">
                <Globe className="h-4 w-4 mr-2" />
                Multi-langues (FR/AR)
              </div>
              <div className="flex items-center text-gray-600">
                <QrCode className="h-4 w-4 mr-2" />
                QR Code automatique
              </div>
              <div className="flex items-center text-gray-600">
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </div>
              <div className="flex items-center text-gray-600">
                <Hash className="h-4 w-4 mr-2" />
                Numérotation auto
              </div>
            </div>
          </div>

          {/* Inventory Settings */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Settings className="h-6 w-6 text-purple-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Inventory Settings</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Low Stock Threshold</label>
                <input
                  type="number"
                  value={settings.lowStockThreshold}
                  onChange={(e) => handleInputChange('lowStockThreshold', parseInt(e.target.value))}
                  min="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">Alert when stock falls below this number</p>
              </div>
            </div>
          </div>

          {/* Warranty Settings */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Shield className="h-6 w-6 text-indigo-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Warranty Settings</h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableWarrantyTracking"
                  checked={settings.enableWarrantyTracking}
                  onChange={(e) => handleInputChange('enableWarrantyTracking', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="enableWarrantyTracking" className="ml-2 text-sm text-gray-700">
                  Enable warranty tracking
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Default Warranty Period (months)</label>
                <input
                  type="number"
                  value={settings.defaultWarrantyPeriod}
                  onChange={(e) => handleInputChange('defaultWarrantyPeriod', parseInt(e.target.value))}
                  min="1"
                  max="60"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Bell className="h-6 w-6 text-yellow-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableNotifications"
                  checked={settings.enableNotifications}
                  onChange={(e) => handleInputChange('enableNotifications', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="enableNotifications" className="ml-2 text-sm text-gray-700">
                  Enable notifications
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoBackup"
                  checked={settings.autoBackup}
                  onChange={(e) => handleInputChange('autoBackup', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="autoBackup" className="ml-2 text-sm text-gray-700">
                  Enable automatic backups
                </label>
              </div>
            </div>
          </div>

          {/* Receipt Settings */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center mb-4">
              <Printer className="h-6 w-6 text-orange-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Receipt Settings</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Receipt Template</label>
                <select
                  value={settings.receiptTemplate}
                  onChange={(e) => handleInputChange('receiptTemplate', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="standard">Standard Template</option>
                  <option value="minimal">Minimal Template</option>
                  <option value="detailed">Detailed Template</option>
                </select>
              </div>
            </div>
          </div>
        </div>

      {/* Save Button (Mobile) */}
      <div className="mt-6 lg:hidden">
        <button
          onClick={handleSave}
          disabled={loading}
          className={`w-full px-4 py-2 rounded-lg flex items-center justify-center ${loading
            ? 'bg-gray-400 cursor-not-allowed'
            : saved
              ? 'bg-green-600 hover:bg-green-700'
              : 'bg-blue-600 hover:bg-blue-700'
            } text-white`}
        >
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          ) : (
            <Settings className="h-5 w-5 mr-2" />
          )}
          {loading ? 'Saving...' : saved ? 'Saved!' : 'Save Settings'}
        </button>
      </div>
    </div>
  );
}
