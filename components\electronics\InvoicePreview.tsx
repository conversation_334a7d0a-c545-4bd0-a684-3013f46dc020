'use client';

import { X, Download, QrCode } from 'lucide-react';

interface InvoiceSettings {
  companyName: string;
  companyAddress: string;
  ice: string;
  rc: string;
  cnss: string;
  taxId: string;
  phone: string;
  email: string;
  website: string;
  logo: string | null;
  logoPosition: 'left' | 'center' | 'right';
  template: 'simple' | 'compact' | 'signature';
  primaryColor: string;
  secondaryColor: string;
  fontSize: 'small' | 'medium' | 'large';
  currency: string;
  locale: 'fr-MA' | 'ar-MA' | 'en-US';
  dateFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
  enableQrCode: boolean;
  qrCodeContent: 'invoice_url' | 'company_info' | 'payment_info';
  autoGenerateNumber: boolean;
  invoicePrefix: string;
  invoiceCounter: number;
  defaultLanguage: 'fr' | 'ar' | 'en';
  enableMultiLanguage: boolean;
  footerText: string;
  termsAndConditions: string;
  bankDetails: string;
  enableSignature: boolean;
  signatureText: string;
}

interface InvoicePreviewProps {
  isOpen: boolean;
  onClose: () => void;
  settings: InvoiceSettings;
}

export default function InvoicePreview({ isOpen, onClose, settings }: InvoicePreviewProps) {
  if (!isOpen) return null;

  const generateInvoiceNumber = () => {
    const { invoicePrefix, invoiceCounter } = settings;
    const year = new Date().getFullYear();
    return `${invoicePrefix}-${year}-${String(invoiceCounter).padStart(4, '0')}`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(settings.locale, {
      style: 'currency',
      currency: settings.currency
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    };
    
    if (settings.locale === 'fr-MA') {
      return date.toLocaleDateString('fr-FR', options);
    } else if (settings.locale === 'ar-MA') {
      return date.toLocaleDateString('ar-MA', options);
    }
    return date.toLocaleDateString('en-US', options);
  };

  const generateQRCode = (content: string) => {
    return `https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=${encodeURIComponent(content)}`;
  };

  const sampleItems = [
    { name: 'iPhone 15 Pro', quantity: 1, price: 12999, total: 12999 },
    { name: 'Coque de protection', quantity: 1, price: 299, total: 299 },
    { name: 'Chargeur rapide', quantity: 1, price: 499, total: 499 }
  ];

  const subtotal = sampleItems.reduce((sum, item) => sum + item.total, 0);
  const tax = subtotal * 0.20; // 20% TVA
  const total = subtotal + tax;

  const fontSizeClass = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg'
  }[settings.fontSize];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Aperçu de la Facture</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => alert('Fonctionnalité d\'export PDF à implémenter')}
              className="px-3 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50"
            >
              <Download className="h-4 w-4 mr-2" />
              PDF
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Invoice Content */}
        <div className="p-8" style={{ color: settings.secondaryColor }}>
          <div className={`${fontSizeClass} max-w-4xl mx-auto bg-white`}>
            {/* Header */}
            <div className="flex items-start justify-between mb-8">
              <div className={`flex items-center ${settings.logoPosition === 'center' ? 'justify-center w-full' : settings.logoPosition === 'right' ? 'justify-end w-full' : ''}`}>
                {settings.logo && (
                  <img
                    src={settings.logo}
                    alt="Logo"
                    className="h-16 w-auto mr-4"
                  />
                )}
                <div className={settings.logoPosition === 'center' ? 'text-center' : settings.logoPosition === 'right' ? 'text-right' : ''}>
                  <h1 className="text-2xl font-bold" style={{ color: settings.primaryColor }}>
                    {settings.companyName}
                  </h1>
                  <div className="text-sm mt-2 whitespace-pre-line">
                    {settings.companyAddress}
                  </div>
                </div>
              </div>
            </div>

            {/* Company Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="font-semibold mb-2" style={{ color: settings.primaryColor }}>
                  Informations Légales
                </h3>
                <div className="text-sm space-y-1">
                  <div>ICE: {settings.ice}</div>
                  <div>RC: {settings.rc}</div>
                  {settings.cnss && <div>CNSS: {settings.cnss}</div>}
                  <div>IF: {settings.taxId}</div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2" style={{ color: settings.primaryColor }}>
                  Contact
                </h3>
                <div className="text-sm space-y-1">
                  <div>Tél: {settings.phone}</div>
                  <div>Email: {settings.email}</div>
                  {settings.website && <div>Web: {settings.website}</div>}
                </div>
              </div>
            </div>

            {/* Invoice Details */}
            <div className="flex justify-between items-start mb-8">
              <div>
                <h2 className="text-xl font-bold mb-2" style={{ color: settings.primaryColor }}>
                  FACTURE
                </h2>
                <div className="text-sm">
                  <div>N°: {generateInvoiceNumber()}</div>
                  <div>Date: {formatDate(new Date())}</div>
                </div>
              </div>
              
              <div className="text-right">
                <h3 className="font-semibold mb-2">Client</h3>
                <div className="text-sm">
                  <div>Nom du Client</div>
                  <div>Adresse du Client</div>
                  <div>Ville, Code Postal</div>
                </div>
              </div>
            </div>

            {/* Items Table */}
            <div className="mb-8">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr style={{ backgroundColor: settings.primaryColor + '20' }}>
                    <th className="border border-gray-300 px-4 py-2 text-left">Article</th>
                    <th className="border border-gray-300 px-4 py-2 text-center">Qté</th>
                    <th className="border border-gray-300 px-4 py-2 text-right">Prix Unit.</th>
                    <th className="border border-gray-300 px-4 py-2 text-right">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {sampleItems.map((item, index) => (
                    <tr key={index}>
                      <td className="border border-gray-300 px-4 py-2">{item.name}</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">{item.quantity}</td>
                      <td className="border border-gray-300 px-4 py-2 text-right">{formatCurrency(item.price)}</td>
                      <td className="border border-gray-300 px-4 py-2 text-right">{formatCurrency(item.total)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Totals */}
            <div className="flex justify-end mb-8">
              <div className="w-64">
                <div className="flex justify-between py-2">
                  <span>Sous-total:</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                <div className="flex justify-between py-2">
                  <span>TVA (20%):</span>
                  <span>{formatCurrency(tax)}</span>
                </div>
                <div className="flex justify-between py-2 font-bold border-t" style={{ color: settings.primaryColor }}>
                  <span>Total:</span>
                  <span>{formatCurrency(total)}</span>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8 pt-8 border-t">
              <div>
                {settings.bankDetails && (
                  <div>
                    <h4 className="font-semibold mb-2">Coordonnées Bancaires</h4>
                    <div className="text-sm whitespace-pre-line">{settings.bankDetails}</div>
                  </div>
                )}
              </div>
              
              <div className="flex justify-between items-end">
                {settings.enableSignature && (
                  <div className="text-center">
                    <div className="border-b border-gray-300 w-32 mb-2"></div>
                    <div className="text-sm">{settings.signatureText}</div>
                  </div>
                )}
                
                {settings.enableQrCode && (
                  <div className="text-center">
                    <img
                      src={generateQRCode(settings.companyName)}
                      alt="QR Code"
                      className="w-16 h-16 mx-auto mb-2"
                    />
                    <div className="text-xs">QR Code</div>
                  </div>
                )}
              </div>
            </div>

            {/* Terms and Footer Text */}
            {(settings.termsAndConditions || settings.footerText) && (
              <div className="mt-8 pt-4 border-t text-xs">
                {settings.termsAndConditions && (
                  <div className="mb-4">
                    <h4 className="font-semibold mb-2">Conditions Générales</h4>
                    <div className="whitespace-pre-line">{settings.termsAndConditions}</div>
                  </div>
                )}
                
                {settings.footerText && (
                  <div className="text-center" style={{ color: settings.primaryColor }}>
                    {settings.footerText}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
