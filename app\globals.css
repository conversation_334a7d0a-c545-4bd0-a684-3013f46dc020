@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=DM+Sans:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background-color: #F8FAFC;
  }
}

/* Hide Next.js badge and error messages */
body>nextjs-portal {
  display: none;
}

/* Modern Design System */
@layer components {
  .arabic-text {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    line-height: 1.8;
    letter-spacing: 0.02em;
  }

  /* New Color Palette */
  .bg-neutral-light {
    background-color: #F8FAFC;
  }

  .moroccan-gradient {
    background: linear-gradient(135deg, #059669 0%, #7C3AED 100%);
  }

  .moroccan-green {
    background-color: #059669;
  }

  .moroccan-purple {
    background-color: #7C3AED;
  }

  .accent-amber {
    background-color: #F59E0B;
  }

  .accent-blue {
    background-color: #3B82F6;
  }

  /* Modern Typography */
  .font-inter {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  }

  .font-dm-sans {
    font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, sans-serif;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Better focus states for accessibility */
  .focus-visible:focus-visible {
    outline: 2px solid #16a34a;
    outline-offset: 2px;
  }

  /* Custom animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  /* Mobile-first responsive text */
  .responsive-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  @media (min-width: 640px) {
    .responsive-text-sm {
      font-size: 1rem;
      line-height: 1.5rem;
    }
  }

  .responsive-text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  @media (min-width: 640px) {
    .responsive-text-base {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }
  }

  /* Modern Card Styles */
  .modern-card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .modern-card:hover {
    @apply shadow-lg border-gray-200;
    transform: translateY(-2px);
  }

  .kpi-card {
    @apply modern-card p-6;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(10px);
  }

  .kpi-card:hover {
    @apply shadow-xl;
    transform: translateY(-4px);
  }

  /* Interactive Elements */
  .btn-primary {
    @apply bg-gradient-to-r from-emerald-600 to-purple-600 text-white font-medium px-6 py-3 rounded-lg;
    transition: all 0.3s ease;
  }

  .btn-primary:hover {
    @apply shadow-lg;
    transform: translateY(-1px);
  }

  .btn-secondary {
    @apply bg-white text-gray-700 border border-gray-200 font-medium px-6 py-3 rounded-lg;
    transition: all 0.3s ease;
  }

  .btn-secondary:hover {
    @apply bg-gray-50 border-gray-300 shadow-md;
  }

  /* Navigation Styles */
  .nav-item {
    @apply flex items-center px-4 py-3 text-gray-700 rounded-lg transition-all duration-200;
  }

  .nav-item:hover {
    @apply bg-gray-100 text-gray-900;
  }

  .nav-item.active {
    @apply bg-gradient-to-r from-emerald-50 to-purple-50 text-emerald-700 border-l-4 border-emerald-600;
  }

  /* Chart Container */
  .chart-container {
    @apply modern-card p-6;
    min-height: 300px;
  }

  /* Mobile Navigation */
  .mobile-nav-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40;
    backdrop-filter: blur(4px);
  }

  .mobile-nav-sidebar {
    @apply fixed left-0 top-0 h-full w-80 bg-white shadow-2xl z-50;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mobile-nav-sidebar.open {
    transform: translateX(0);
  }

  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
  }

  [dir="rtl"] .mobile-nav-sidebar {
    @apply fixed right-0 left-auto;
    transform: translateX(100%);
  }

  [dir="rtl"] .mobile-nav-sidebar.open {
    transform: translateX(0);
  }

  [dir="rtl"] .nav-item.active {
    @apply border-l-0 border-r-4 border-emerald-600;
  }

  /* Arabic font support */
  [dir="rtl"] body {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  }

  /* RTL text alignment */
  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  /* RTL margin and padding adjustments */
  [dir="rtl"] .ml-auto {
    margin-left: 0;
    margin-right: auto;
  }

  [dir="rtl"] .mr-auto {
    margin-right: 0;
    margin-left: auto;
  }

  /* RTL flex adjustments */
  [dir="rtl"] .justify-start {
    justify-content: flex-end;
  }

  [dir="rtl"] .justify-end {
    justify-content: flex-start;
  }

  /* Better Arabic text rendering */
  [dir="rtl"] .arabic-text {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    line-height: 1.8;
    letter-spacing: 0.02em;
    text-align: right;
  }

  /* RTL specific animations */
  [dir="rtl"] .animate-slide-in-left {
    animation: slideInRight 0.3s ease-out;
  }

  [dir="rtl"] .animate-slide-in-right {
    animation: slideInLeft 0.3s ease-out;
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
    }

    to {
      transform: translateX(0);
    }
  }

  @keyframes slideInLeft {
    from {
      transform: translateX(-100%);
    }

    to {
      transform: translateX(0);
    }
  }
}