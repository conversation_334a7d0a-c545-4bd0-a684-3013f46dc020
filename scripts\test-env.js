#!/usr/bin/env node

/**
 * Test script to verify environment variables are loaded correctly
 */

require('dotenv').config({ path: '.env.local' });

console.log('🔍 Testing environment variables...\n');

const jwtSecret = process.env.JWT_SECRET;
const mongoUri = process.env.MONGODB_URI;

console.log('Environment Variables Status:');
console.log('============================');

if (jwtSecret) {
  console.log(`✅ JWT_SECRET: Found (${jwtSecret.length} characters)`);
  if (jwtSecret.length >= 32) {
    console.log('✅ JWT_SECRET length: Valid (≥32 characters)');
  } else {
    console.log(`❌ JWT_SECRET length: Invalid (${jwtSecret.length} < 32 characters)`);
  }
} else {
  console.log('❌ JWT_SECRET: Not found');
}

if (mongoUri) {
  console.log(`✅ MONGODB_URI: Found`);
} else {
  console.log('❌ MONGODB_URI: Not found');
}

console.log(`✅ NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
console.log(`✅ NEXT_PUBLIC_APP_URL: ${process.env.NEXT_PUBLIC_APP_URL || 'not set'}`);

console.log('\n🎯 Environment test complete!');

if (jwtSecret && jwtSecret.length >= 32) {
  console.log('✅ All security requirements met. You can start the application.');
  process.exit(0);
} else {
  console.log('❌ Security requirements not met. Please check your .env.local file.');
  process.exit(1);
}
