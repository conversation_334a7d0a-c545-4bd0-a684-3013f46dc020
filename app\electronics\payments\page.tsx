'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Plus, 
  Search,
  CreditCard,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Filter,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Receipt
} from 'lucide-react';

interface Payment {
  id: string;
  paymentNumber: string;
  customerName: string;
  customerId: string;
  amount: number;
  type: 'payment' | 'credit' | 'debt';
  method: 'cash' | 'card' | 'transfer' | 'check';
  date: string;
  description: string;
  invoiceNumber?: string;
  status: 'completed' | 'pending' | 'failed';
  balance: number; // Running balance for the customer
}

interface CustomerBalance {
  customerId: string;
  customerName: string;
  totalCredit: number;
  totalDebt: number;
  balance: number; // Positive = customer owes us, Negative = we owe customer
}

export default function PaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [customerBalances, setCustomerBalances] = useState<CustomerBalance[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [methodFilter, setMethodFilter] = useState('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - replace with actual API call
    const mockPayments: Payment[] = [
      {
        id: '1',
        paymentNumber: 'PAY-2024-001',
        customerName: 'John Doe',
        customerId: '1',
        amount: 1130.8,
        type: 'payment',
        method: 'card',
        date: '2024-01-15',
        description: 'Payment for Invoice INV-2024-001',
        invoiceNumber: 'INV-2024-001',
        status: 'completed',
        balance: 0
      },
      {
        id: '2',
        paymentNumber: 'CRD-2024-001',
        customerName: 'Jane Smith',
        customerId: '2',
        amount: 500,
        type: 'credit',
        method: 'cash',
        date: '2024-01-16',
        description: 'Store credit for returned item',
        status: 'completed',
        balance: -500
      },
      {
        id: '3',
        paymentNumber: 'DBT-2024-001',
        customerName: 'Bob Johnson',
        customerId: '3',
        amount: 1428.9,
        type: 'debt',
        method: 'transfer',
        date: '2024-01-10',
        description: 'Outstanding debt for Invoice INV-2024-003',
        invoiceNumber: 'INV-2024-003',
        status: 'pending',
        balance: 1428.9
      },
      {
        id: '4',
        paymentNumber: 'PAY-2024-002',
        customerName: 'Alice Brown',
        customerId: '4',
        amount: 750,
        type: 'payment',
        method: 'cash',
        date: '2024-01-17',
        description: 'Partial payment for services',
        status: 'completed',
        balance: 250
      }
    ];

    // Calculate customer balances
    const balances: { [key: string]: CustomerBalance } = {};
    
    mockPayments.forEach(payment => {
      if (!balances[payment.customerId]) {
        balances[payment.customerId] = {
          customerId: payment.customerId,
          customerName: payment.customerName,
          totalCredit: 0,
          totalDebt: 0,
          balance: 0
        };
      }

      const customerBalance = balances[payment.customerId];
      
      if (payment.type === 'payment') {
        // Payment reduces debt
        customerBalance.balance -= payment.amount;
      } else if (payment.type === 'credit') {
        // Credit means we owe the customer
        customerBalance.totalCredit += payment.amount;
        customerBalance.balance -= payment.amount;
      } else if (payment.type === 'debt') {
        // Debt means customer owes us
        customerBalance.totalDebt += payment.amount;
        customerBalance.balance += payment.amount;
      }
    });

    setPayments(mockPayments);
    setCustomerBalances(Object.values(balances));
    setLoading(false);
  }, []);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'payment': return 'bg-green-100 text-green-800';
      case 'credit': return 'bg-blue-100 text-blue-800';
      case 'debt': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'cash': return <DollarSign className="h-4 w-4" />;
      case 'card': return <CreditCard className="h-4 w-4" />;
      case 'transfer': return <TrendingUp className="h-4 w-4" />;
      case 'check': return <Receipt className="h-4 w-4" />;
      default: return <DollarSign className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.paymentNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || payment.type === typeFilter;
    const matchesMethod = methodFilter === 'all' || payment.method === methodFilter;
    return matchesSearch && matchesType && matchesMethod;
  });

  const totalPayments = payments.filter(p => p.type === 'payment').reduce((sum, p) => sum + p.amount, 0);
  const totalCredits = payments.filter(p => p.type === 'credit').reduce((sum, p) => sum + p.amount, 0);
  const totalDebts = payments.filter(p => p.type === 'debt').reduce((sum, p) => sum + p.amount, 0);
  const netBalance = totalDebts - totalCredits;

  const typeOptions = [
    { value: 'all', label: 'All Types' },
    { value: 'payment', label: 'Payments' },
    { value: 'credit', label: 'Credits' },
    { value: 'debt', label: 'Debts' }
  ];

  const methodOptions = [
    { value: 'all', label: 'All Methods' },
    { value: 'cash', label: 'Cash' },
    { value: 'card', label: 'Card' },
    { value: 'transfer', label: 'Transfer' },
    { value: 'check', label: 'Check' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payments & Credits</h1>
          <p className="text-gray-600">Manage payments, credits, and customer balances</p>
        </div>
        <Link 
          href="/electronics/payments/new"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-blue-700"
        >
          <Plus className="h-5 w-5 mr-2" />
          Record Payment
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Payments</p>
              <p className="text-2xl font-bold text-gray-900">${totalPayments.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <TrendingDown className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Credits</p>
              <p className="text-2xl font-bold text-gray-900">${totalCredits.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <CreditCard className="h-8 w-8 text-red-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Outstanding Debts</p>
              <p className="text-2xl font-bold text-gray-900">${totalDebts.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <DollarSign className={`h-8 w-8 ${netBalance >= 0 ? 'text-green-500' : 'text-red-500'}`} />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Net Balance</p>
              <p className={`text-2xl font-bold ${netBalance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                ${Math.abs(netBalance).toFixed(2)}
              </p>
              <p className="text-xs text-gray-500">
                {netBalance >= 0 ? 'Owed to us' : 'We owe'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search payments by number, customer, or description..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              {typeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={methodFilter}
              onChange={(e) => setMethodFilter(e.target.value)}
            >
              {methodOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Customer Balances Summary */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Balances</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {customerBalances.map((customer) => (
            <div key={customer.customerId} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{customer.customerName}</h4>
                <span className={`text-sm font-bold ${
                  customer.balance > 0 ? 'text-red-600' : customer.balance < 0 ? 'text-blue-600' : 'text-green-600'
                }`}>
                  ${Math.abs(customer.balance).toFixed(2)}
                </span>
              </div>
              <p className="text-xs text-gray-500">
                {customer.balance > 0 ? 'Owes us' : customer.balance < 0 ? 'We owe' : 'Balanced'}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Payments Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Payment
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Method
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredPayments.map((payment) => (
              <tr key={payment.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{payment.paymentNumber}</div>
                  <div className="text-sm text-gray-500">{payment.description}</div>
                  {payment.invoiceNumber && (
                    <div className="text-xs text-blue-600">Ref: {payment.invoiceNumber}</div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-400 mr-2" />
                    <div className="text-sm font-medium text-gray-900">{payment.customerName}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className={`text-sm font-bold ${
                    payment.type === 'payment' ? 'text-green-600' : 
                    payment.type === 'credit' ? 'text-blue-600' : 'text-red-600'
                  }`}>
                    {payment.type === 'credit' ? '-' : ''}${payment.amount.toFixed(2)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(payment.type)}`}>
                    {payment.type.charAt(0).toUpperCase() + payment.type.slice(1)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {getMethodIcon(payment.method)}
                    <span className="ml-2 text-sm text-gray-900 capitalize">{payment.method}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    {new Date(payment.date).toLocaleDateString()}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payment.status)}`}>
                    {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="text-green-600 hover:text-green-900">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="text-red-600 hover:text-red-900">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
